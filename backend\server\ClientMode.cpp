#include "ClientMode.h"
#include <QNetworkRequest>
#include <QJsonParseError>
#include <QDebug>

ClientMode::ClientMode(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_connected(false)
    , m_autoReconnect(false)
    , m_currentTestReply(nullptr)
    , m_currentImageListReply(nullptr)
{
    m_connectionStatus = "未连接";
}

ClientMode::~ClientMode()
{
    disconnectFromServer();
}

void ClientMode::connectToServer(const QString &host, int port)
{
    if (host.isEmpty()) {
        setConnectionStatus("错误：服务器地址为空");
        emit connectionError("服务器地址不能为空");
        return;
    }

    m_serverUrl = formatServerUrl(host, port);
    setConnectionStatus("正在连接...");

    qDebug() << "尝试连接到服务器:" << m_serverUrl;
    testConnection();
}

void ClientMode::disconnectFromServer()
{
    if (m_currentTestReply) {
        m_currentTestReply->abort();
        m_currentTestReply = nullptr;
    }

    if (m_currentImageListReply) {
        m_currentImageListReply->abort();
        m_currentImageListReply = nullptr;
    }

    setConnected(false);
    setConnectionStatus("已断开连接");
    m_imageList = QJsonArray();
    emit imageListChanged();
}

void ClientMode::testConnection()
{
    if (m_currentTestReply) {
        m_currentTestReply->abort();
    }

    QUrl testUrl(m_serverUrl + "/api/images?path=/");
    QNetworkRequest request(testUrl);
    request.setRawHeader("User-Agent", "ImageViewer-Client/1.0");
    request.setAttribute(QNetworkRequest::RedirectPolicyAttribute, QNetworkRequest::NoLessSafeRedirectPolicy);

    m_currentTestReply = m_networkManager->get(request);
    connect(m_currentTestReply, &QNetworkReply::finished, this, &ClientMode::onTestConnectionFinished);
    connect(m_currentTestReply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::errorOccurred),
            this, &ClientMode::onNetworkError);

    qDebug() << "发送测试连接请求到:" << testUrl.toString();
}

void ClientMode::loadImageList(const QString &path)
{
    if (!m_connected) {
        qWarning() << "未连接到服务器，无法加载图片列表";
        return;
    }

    if (m_currentImageListReply) {
        m_currentImageListReply->abort();
    }

    QString apiPath = path.isEmpty() ? "/" : path;
    QUrl listUrl(m_serverUrl + "/api/images?path=" + QUrl::toPercentEncoding(apiPath));
    QNetworkRequest request(listUrl);
    request.setRawHeader("User-Agent", "ImageViewer-Client/1.0");

    m_currentImageListReply = m_networkManager->get(request);
    connect(m_currentImageListReply, &QNetworkReply::finished, this, &ClientMode::onImageListFinished);
    connect(m_currentImageListReply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::errorOccurred),
            this, &ClientMode::onNetworkError);

    qDebug() << "加载图片列表:" << listUrl.toString();
}

QString ClientMode::getImageUrl(const QString &imagePath)
{
    if (!m_connected || imagePath.isEmpty()) {
        return QString();
    }

    return m_serverUrl + "/api/image/" + QUrl::toPercentEncoding(imagePath);
}

QString ClientMode::getThumbnailUrl(const QString &imagePath)
{
    if (!m_connected || imagePath.isEmpty()) {
        return QString();
    }

    return m_serverUrl + "/api/thumbnail/" + QUrl::toPercentEncoding(imagePath);
}

void ClientMode::onTestConnectionFinished()
{
    if (!m_currentTestReply) {
        return;
    }

    QNetworkReply::NetworkError error = m_currentTestReply->error();
    if (error == QNetworkReply::NoError) {
        QByteArray data = m_currentTestReply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

        if (parseError.error == QJsonParseError::NoError && doc.isObject()) {
            setConnected(true);
            setConnectionStatus("已连接");
            qDebug() << "成功连接到服务器";

            // 自动加载根目录图片列表
            loadImageList("");
        } else {
            setConnected(false);
            setConnectionStatus("服务器响应格式错误");
            emit connectionError("服务器响应格式不正确");
        }
    } else {
        setConnected(false);
        QString errorMsg = QString("连接失败: %1").arg(m_currentTestReply->errorString());
        setConnectionStatus(errorMsg);
        emit connectionError(errorMsg);
        qWarning() << "连接测试失败:" << errorMsg;
    }

    m_currentTestReply->deleteLater();
    m_currentTestReply = nullptr;
}

void ClientMode::onImageListFinished()
{
    if (!m_currentImageListReply) {
        return;
    }

    QNetworkReply::NetworkError error = m_currentImageListReply->error();
    if (error == QNetworkReply::NoError) {
        QByteArray data = m_currentImageListReply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

        if (parseError.error == QJsonParseError::NoError && doc.isObject()) {
            QJsonObject obj = doc.object();
            if (obj.contains("images") && obj["images"].isArray()) {
                QJsonArray images = obj["images"].toArray();
                updateImageList(images);
                emit imageListLoaded(images);
                qDebug() << "成功加载图片列表，共" << images.size() << "项";
            } else {
                qWarning() << "图片列表响应格式错误";
            }
        } else {
            qWarning() << "图片列表JSON解析失败:" << parseError.errorString();
        }
    } else {
        qWarning() << "加载图片列表失败:" << m_currentImageListReply->errorString();
    }

    m_currentImageListReply->deleteLater();
    m_currentImageListReply = nullptr;
}

void ClientMode::onNetworkError(QNetworkReply::NetworkError error)
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    QString errorMsg = QString("网络错误 (%1): %2").arg(static_cast<int>(error)).arg(reply->errorString());
    qWarning() << errorMsg;

    if (reply == m_currentTestReply) {
        setConnected(false);
        setConnectionStatus(errorMsg);
        emit connectionError(errorMsg);
    }
}

void ClientMode::setConnected(bool connected)
{
    if (m_connected != connected) {
        m_connected = connected;
        emit connectedChanged();
    }
}

void ClientMode::setConnectionStatus(const QString &status)
{
    if (m_connectionStatus != status) {
        m_connectionStatus = status;
        emit connectionStatusChanged();
    }
}

void ClientMode::updateImageList(const QJsonArray &images)
{
    m_imageList = images;
    emit imageListChanged();
}

QString ClientMode::formatServerUrl(const QString &host, int port)
{
    QString cleanHost = host.trimmed();

    // 如果已经包含协议，直接使用
    if (cleanHost.startsWith("http://") || cleanHost.startsWith("https://")) {
        return cleanHost;
    }

    // 否则添加http协议和端口
    return QString("http://%1:%2").arg(cleanHost).arg(port);
}

// 兼容性方法实现
void ClientMode::connectToServer(const std::string& address, int port)
{
    connectToServer(QString::fromStdString(address), port);
}

void ClientMode::enableAutoReconnect(bool enable)
{
    m_autoReconnect = enable;
    // TODO: 实现自动重连逻辑
}

void ClientMode::discoverServers(std::function<void(const std::vector<std::string>&)> handler)
{
    m_discoveryHandler = handler;
    // TODO: 实现服务发现逻辑
    // 暂时返回空列表
    if (handler) {
        handler(std::vector<std::string>());
    }
}