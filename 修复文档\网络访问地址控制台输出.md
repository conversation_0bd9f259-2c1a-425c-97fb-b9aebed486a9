# 网络访问地址控制台输出功能

## ✅ **功能完成**

在网络服务启动时，控制台会显示详细的访问地址信息，方便用户了解如何访问服务。

## 📋 **输出内容**

### 🖥️ **控制台输出示例**
```
Image server started on port 3333
=== 网络访问地址 ===
本地访问: http://127.0.0.1:3333
本地访问: http://localhost:3333
局域网访问地址:
  http://*************:3333
  http://*********:3333
IPv6访问地址:
  http://[2001:db8::1]:3333
API示例:
  获取文件列表: http://*************:3333/api/images?path=/
  获取图片: http://*************:3333/api/image/图片名.jpg
==================
```

## 🌐 **地址类型说明**

### 1. **本地访问地址**
```
http://127.0.0.1:3333
http://localhost:3333
```
- ✅ **用途**：仅限本机访问
- ✅ **适用场景**：本地测试、开发调试

### 2. **局域网访问地址**
```
http://*************:3333
http://*********:3333
```
- ✅ **用途**：局域网内其他设备访问
- ✅ **适用场景**：家庭网络、办公室网络内共享
- ✅ **安全性**：仅局域网内可访问

### 3. **IPv6访问地址**
```
http://[2001:db8::1]:3333
```
- ✅ **用途**：IPv6网络访问
- ✅ **适用场景**：支持IPv6的现代网络环境

## 🎯 **实际使用场景**

### 📱 **移动设备访问**
```
# 手机连接同一WiFi网络
http://*************:3333/api/images?path=/
```

### 💻 **其他电脑访问**
```
# 同一局域网内的其他电脑
http://*************:3333/api/image/wallhaven-5gvok7.webp
```

### 🌐 **Web应用集成**
```javascript
// 前端代码示例
const serverIP = "*************";
const serverPort = "3333";
const apiBase = `http://${serverIP}:${serverPort}`;

// 获取图片列表
fetch(`${apiBase}/api/images?path=/`)
  .then(response => response.json())
  .then(data => console.log(data));
```

## 🔧 **技术实现**

### 📋 **地址获取逻辑**
```cpp
// 输出本地访问地址
qInfo() << "本地访问: http://127.0.0.1:" << m_currentPort;
qInfo() << "本地访问: http://localhost:" << m_currentPort;

// 输出局域网地址
if (!m_ipv4Addresses.isEmpty()) {
    qInfo() << "局域网访问地址:";
    for (const QString& ip : m_ipv4Addresses) {
        qInfo() << "  http://" << ip << ":" << m_currentPort;
    }
}

// 输出IPv6地址
if (!m_ipv6Addresses.isEmpty()) {
    qInfo() << "IPv6访问地址:";
    for (const QString& ip : m_ipv6Addresses) {
        qInfo() << "  http://[" << ip << "]:" << m_currentPort;
    }
}
```

### 🌐 **网络接口过滤**
系统会自动过滤掉以下接口：
- ✅ **回环接口**：127.0.0.1 (单独显示)
- ✅ **虚拟接口**：VMware、VirtualBox等
- ✅ **蓝牙接口**：Bluetooth相关接口
- ✅ **点对点接口**：PPP连接等

只显示真实的网络接口地址。

## 📊 **用户体验**

### ✅ **优势**
1. **一目了然**：启动服务后立即看到所有可用地址
2. **复制方便**：可直接复制控制台中的URL使用
3. **示例清晰**：提供具体的API使用示例
4. **分类明确**：本地、局域网、IPv6地址分别显示

### 🎯 **使用建议**
1. **本地测试**：使用 `127.0.0.1` 地址
2. **局域网共享**：使用显示的局域网IP地址
3. **移动设备**：确保设备连接同一WiFi网络
4. **防火墙**：确保端口3333未被防火墙阻止

## 🛡️ **安全提醒**

### ⚠️ **注意事项**
1. **局域网访问**：仅在可信网络环境中使用
2. **端口开放**：确认防火墙设置允许相应端口
3. **监听文件夹**：只共享必要的文件夹
4. **网络环境**：避免在公共WiFi中开启服务

### 🔒 **安全建议**
- 仅在需要时启用网络服务
- 定期检查监听文件夹内容
- 使用完毕后及时关闭服务
- 避免共享敏感文件

## 🎉 **功能完成**

现在启动网络服务时，控制台会清晰显示：
- ✅ **本地访问地址**
- ✅ **局域网访问地址**  
- ✅ **IPv6访问地址**
- ✅ **API使用示例**

用户可以根据需要选择合适的访问地址，方便在不同设备和网络环境中使用图片共享功能！🚀
