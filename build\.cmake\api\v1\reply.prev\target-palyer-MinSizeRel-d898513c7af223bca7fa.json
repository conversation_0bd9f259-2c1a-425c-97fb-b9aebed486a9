{"artifacts": [{"path": "MinSizeRel/palyer.exe"}, {"path": "MinSizeRel/palyer.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "qt6_add_qml_module", "qt_add_qml_module", "_qt_internal_find_third_party_dependencies", "add_dependencies", "_qt_internal_scan_qml_imports", "_qt_internal_generate_deploy_qml_imports_script", "cmake_language", "_qt_internal_finalize_executable", "qt6_finalize_target", "_qt_internal_qml_copy_files_to_build_dir", "qt6_target_qml_sources", "target_compile_options", "target_include_directories", "_qt_internal_qml_type_registration", "target_sources", "qt6_extract_metatypes", "set_source_files_properties", "__qt_propagate_generated_resource", "_qt_internal_process_resource", "qt6_add_resources", "_qt_internal_target_enable_qmlcachegen", "_qt_internal_expose_deferred_files_to_ide"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapAtomic.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake:787:EVAL"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 14, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 71, "parent": 0}, {"command": 5, "file": 1, "line": 46, "parent": 0}, {"command": 5, "file": 1, "line": 41, "parent": 0}, {"command": 8, "file": 1, "line": 9, "parent": 0}, {"file": 4, "parent": 8}, {"command": 8, "file": 4, "line": 218, "parent": 9}, {"file": 3, "parent": 10}, {"command": 7, "file": 3, "line": 55, "parent": 11}, {"file": 2, "parent": 12}, {"command": 6, "file": 2, "line": 61, "parent": 13}, {"command": 7, "file": 3, "line": 43, "parent": 11}, {"file": 9, "parent": 15}, {"command": 10, "file": 9, "line": 45, "parent": 16}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 18}, {"file": 6, "parent": 19}, {"command": 7, "file": 6, "line": 55, "parent": 20}, {"file": 5, "parent": 21}, {"command": 6, "file": 5, "line": 61, "parent": 22}, {"command": 12, "file": 1, "line": 27, "parent": 0}, {"command": 11, "file": 10, "line": 1252, "parent": 24}, {"command": 5, "file": 10, "line": 596, "parent": 25}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 27}, {"file": 12, "parent": 28}, {"command": 7, "file": 12, "line": 55, "parent": 29}, {"file": 11, "parent": 30}, {"command": 6, "file": 11, "line": 61, "parent": 31}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 33}, {"file": 14, "parent": 34}, {"command": 7, "file": 14, "line": 58, "parent": 35}, {"file": 13, "parent": 36}, {"command": 6, "file": 13, "line": 61, "parent": 37}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 40}, {"file": 16, "parent": 41}, {"command": 7, "file": 16, "line": 57, "parent": 42}, {"file": 15, "parent": 43}, {"command": 6, "file": 15, "line": 61, "parent": 44}, {"command": 7, "file": 16, "line": 45, "parent": 42}, {"file": 18, "parent": 46}, {"command": 13, "file": 18, "line": 35, "parent": 47}, {"command": 9, "file": 8, "line": 36, "parent": 48}, {"command": 8, "file": 7, "line": 76, "parent": 49}, {"file": 17, "parent": 50}, {"command": 5, "file": 17, "line": 48, "parent": 51}, {"command": 10, "file": 18, "line": 46, "parent": 47}, {"command": 9, "file": 8, "line": 137, "parent": 53}, {"command": 8, "file": 7, "line": 76, "parent": 54}, {"file": 20, "parent": 55}, {"command": 7, "file": 20, "line": 55, "parent": 56}, {"file": 19, "parent": 57}, {"command": 6, "file": 19, "line": 61, "parent": 58}, {"command": 7, "file": 14, "line": 46, "parent": 35}, {"file": 23, "parent": 60}, {"command": 10, "file": 23, "line": 45, "parent": 61}, {"command": 9, "file": 8, "line": 137, "parent": 62}, {"command": 8, "file": 7, "line": 76, "parent": 63}, {"file": 22, "parent": 64}, {"command": 7, "file": 22, "line": 55, "parent": 65}, {"file": 21, "parent": 66}, {"command": 6, "file": 21, "line": 61, "parent": 67}, {"file": 1, "line": -1, "parent": 0}, {"command": 19, "file": 24, "line": 1, "parent": 69}, {"command": 18, "file": 0, "line": 818, "parent": 70}, {"command": 17, "file": 0, "line": 740, "parent": 71}, {"command": 16, "file": 0, "line": 740, "parent": 72}, {"command": 15, "file": 10, "line": 4401, "parent": 73}, {"command": 14, "file": 10, "line": 4176, "parent": 74}, {"command": 21, "file": 10, "line": 916, "parent": 25}, {"command": 20, "file": 10, "line": 3497, "parent": 76}, {"command": 14, "file": 10, "line": 2860, "parent": 77}, {"command": 20, "file": 10, "line": 3503, "parent": 76}, {"command": 14, "file": 10, "line": 2860, "parent": 79}, {"command": 22, "file": 1, "line": 23, "parent": 0}, {"command": 24, "file": 10, "line": 796, "parent": 25}, {"command": 23, "file": 10, "line": 3879, "parent": 82}, {"command": 23, "file": 10, "line": 3904, "parent": 82}, {"command": 26, "file": 10, "line": 3704, "parent": 82}, {"command": 25, "file": 0, "line": 1352, "parent": 85}, {"command": 25, "file": 0, "line": 1486, "parent": 85}, {"command": 25, "file": 10, "line": 3870, "parent": 82}, {"command": 27, "file": 10, "line": 3894, "parent": 82}, {"command": 30, "file": 10, "line": 850, "parent": 25}, {"command": 29, "file": 0, "line": 401, "parent": 90}, {"command": 28, "file": 0, "line": 2528, "parent": 91}, {"command": 25, "file": 0, "line": 2072, "parent": 92}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 31, "file": 10, "line": 3342, "parent": 76}, {"command": 25, "file": 10, "line": 1696, "parent": 95}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 25, "file": 10, "line": 3205, "parent": 76}, {"command": 25, "file": 10, "line": 3398, "parent": 76}, {"command": 30, "file": 10, "line": 3519, "parent": 76}, {"command": 29, "file": 0, "line": 401, "parent": 112}, {"command": 28, "file": 0, "line": 2528, "parent": 113}, {"command": 25, "file": 0, "line": 2072, "parent": 114}, {"command": 32, "file": 0, "line": 812, "parent": 70}, {"command": 25, "file": 0, "line": 2197, "parent": 116}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 81, "fragment": "/O2"}, {"backtrace": 81, "fragment": "/Ot"}, {"backtrace": 81, "fragment": "/MP"}], "defines": [{"backtrace": 39, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 39, "define": "QT_CORE_LIB"}, {"backtrace": 7, "define": "QT_GUI_LIB"}, {"backtrace": 39, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 26, "define": "QT_NETWORK_LIB"}, {"backtrace": 39, "define": "QT_NO_DEBUG"}, {"backtrace": 7, "define": "QT_OPENGL_LIB"}, {"backtrace": 26, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 7, "define": "QT_QMLMETA_LIB"}, {"backtrace": 7, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 7, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 26, "define": "QT_QML_LIB"}, {"backtrace": 7, "define": "QT_QUICK_LIB"}, {"backtrace": 39, "define": "UNICODE"}, {"backtrace": 39, "define": "WIN32"}, {"backtrace": 39, "define": "WIN64"}, {"backtrace": 39, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 39, "define": "_UNICODE"}, {"backtrace": 39, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Qt/file/palyer/build/palyer_autogen/include_MinSizeRel"}, {"backtrace": 83, "path": "C:/Qt/file/palyer"}, {"backtrace": 6, "path": "C:/Qt/file/palyer/backend"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQuick"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlModels"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"}], "language": "CXX", "languageStandard": {"backtraces": [39], "standard": "17"}, "sourceIndexes": [0, 1, 6, 8, 9, 11, 13, 15, 17, 19, 21, 23, 24, 28]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 81, "fragment": "/O2"}, {"backtrace": 81, "fragment": "/Ot"}, {"backtrace": 81, "fragment": "/MP"}, {"backtrace": 89, "fragment": "/bigobj"}], "defines": [{"backtrace": 39, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 39, "define": "QT_CORE_LIB"}, {"backtrace": 7, "define": "QT_GUI_LIB"}, {"backtrace": 39, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 26, "define": "QT_NETWORK_LIB"}, {"backtrace": 39, "define": "QT_NO_DEBUG"}, {"backtrace": 7, "define": "QT_OPENGL_LIB"}, {"backtrace": 26, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 7, "define": "QT_QMLMETA_LIB"}, {"backtrace": 7, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 7, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 26, "define": "QT_QML_LIB"}, {"backtrace": 7, "define": "QT_QUICK_LIB"}, {"backtrace": 39, "define": "UNICODE"}, {"backtrace": 39, "define": "WIN32"}, {"backtrace": 39, "define": "WIN64"}, {"backtrace": 39, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 39, "define": "_UNICODE"}, {"backtrace": 39, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Qt/file/palyer/build/palyer_autogen/include_MinSizeRel"}, {"backtrace": 83, "path": "C:/Qt/file/palyer"}, {"backtrace": 6, "path": "C:/Qt/file/palyer/backend"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration"}, {"backtrace": 84, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQuick"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlModels"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript"}, {"backtrace": 7, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"}], "language": "CXX", "languageStandard": {"backtraces": [39], "standard": "17"}, "sourceIndexes": [5]}], "dependencies": [{"id": "palyer_automoc_json_extraction::@6890427a1f51a3e7e1df"}, {"backtrace": 75, "id": "palyer_qmlimportscan::@6890427a1f51a3e7e1df"}, {"backtrace": 78, "id": "palyer_copy_qml::@6890427a1f51a3e7e1df"}, {"backtrace": 80, "id": "palyer_copy_res::@6890427a1f51a3e7e1df"}, {"backtrace": 6, "id": "backend::@e17dcb4e28158375c849"}, {"backtrace": 0, "id": "palyer_autogen::@6890427a1f51a3e7e1df"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "palyer::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files/palyer"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "backend\\MinSizeRel\\backend.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Quick.a", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlMeta.a", "role": "libraries"}, {"backtrace": 23, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlWorkerScript.a", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlModels.a", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Qml.a", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6OpenGL.a", "role": "libraries"}, {"backtrace": 14, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 32, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 45, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 45, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 52, "fragment": "-latomic", "role": "libraries"}, {"backtrace": 59, "fragment": "mingw32.lib", "role": "libraries"}, {"backtrace": 59, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 38, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "ws2_32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "palyer", "nameOnDisk": "palyer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 7, 10, 12, 14, 16, 18, 20, 22, 28]}, {"name": "Resources", "sourceIndexes": [2]}, {"name": "Source Files\\Generated", "sourceIndexes": [3, 4, 5, 6, 8, 9, 11, 13, 15, 17, 19, 21, 23, 24, 25, 26, 27]}, {"name": "CMake Rules", "sourceIndexes": [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42]}, {"name": "", "sourceIndexes": [43, 44, 45, 46, 47, 48, 49, 50, 51]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/palyer_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "res.qrc", "sourceGroupIndex": 1}, {"backtrace": 86, "isGenerated": true, "path": "build/ddf_palyer.h", "sourceGroupIndex": 2}, {"backtrace": 87, "isGenerated": true, "path": "build/meta_types/qt6palyer_metatypes.json.gen", "sourceGroupIndex": 2}, {"backtrace": 88, "compileGroupIndex": 1, "isGenerated": true, "path": "build/palyer_qmltyperegistrations.cpp", "sourceGroupIndex": 2}, {"backtrace": 93, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.qt/rcc/qrc_qmake_palyer.cpp", "sourceGroupIndex": 2}, {"backtrace": 94, "path": "Main.qml", "sourceGroupIndex": 0}, {"backtrace": 96, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_qmlcache_loader.cpp", "sourceGroupIndex": 2}, {"backtrace": 97, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_Main_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 98, "path": "compoment/AppState.qml", "sourceGroupIndex": 0}, {"backtrace": 99, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_compoment/AppState_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 100, "path": "compoment/ImagePreviewWindow.qml", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_compoment/ImagePreviewWindow_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 102, "path": "compoment/NetworkSettingsDialog.qml", "sourceGroupIndex": 0}, {"backtrace": 103, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_compoment/NetworkSettingsDialog_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 104, "path": "compoment/SettingsPage.qml", "sourceGroupIndex": 0}, {"backtrace": 105, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_compoment/SettingsPage_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 106, "path": "compoment/imagePage.qml", "sourceGroupIndex": 0}, {"backtrace": 107, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_compoment/imagePage_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 108, "path": "compoment/template/BaseWindowTemplate.qml", "sourceGroupIndex": 0}, {"backtrace": 109, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_compoment/template/BaseWindowTemplate_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 110, "path": "compoment/toolbar/ConversionWindow.qml", "sourceGroupIndex": 0}, {"backtrace": 111, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/palyer_compoment/toolbar/ConversionWindow_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 115, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.qt/rcc/qrc_palyer_raw_qml_0.cpp", "sourceGroupIndex": 2}, {"backtrace": 117, "isGenerated": true, "path": "build/.qt/rcc/qmake_palyer.qrc", "sourceGroupIndex": 2}, {"backtrace": 117, "isGenerated": true, "path": "build/palyer/qmldir", "sourceGroupIndex": 2}, {"backtrace": 117, "isGenerated": true, "path": "build/.qt/rcc/palyer_raw_qml_0.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/palyer_autogen/EWIEGA46WW_MinSizeRel/qrc_res.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/134fcb19f6c0a10249edc172026282a2/qt6palyer_metatypes.json.gen.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/2028906c1413333e401fcd3264098cea/palyer_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/df123ab82d46ae810a2ddd7f4b8d533c/qrc_qmake_palyer.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/7edb533fa42cf3d6641d1bf560752cbd/palyer_qmlcache_loader.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/7edb533fa42cf3d6641d1bf560752cbd/palyer_Main_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/AppState_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/ImagePreviewWindow_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/NetworkSettingsDialog_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/SettingsPage_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/imagePage_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/89d2c1d9975736725dd82f98ce51f0bc/BaseWindowTemplate_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/99284a222e026054a1227c2ee3702b47/ConversionWindow_qml.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/df123ab82d46ae810a2ddd7f4b8d533c/qrc_palyer_raw_qml_0.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/bd86fbaadef8283d96080ea2df14f75e/qrc_res.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 39, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6core_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 26, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qml_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 26, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6network_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 7, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6quick_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 7, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6gui_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 7, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlmeta_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 7, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlmodels_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 7, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlworkerscript_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}, {"backtrace": 7, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6opengl_relwithdebinfo_metatypes.json", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}