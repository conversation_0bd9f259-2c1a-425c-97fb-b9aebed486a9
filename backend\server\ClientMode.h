#pragma once

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QTimer>
#include <QUrl>
#include <QString>
#include <functional>
#include <vector>

class ClientMode : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool connected READ isConnected NOTIFY connectedChanged)
    Q_PROPERTY(QString serverUrl READ serverUrl NOTIFY serverUrlChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY connectionStatusChanged)
    Q_PROPERTY(QJsonArray imageList READ imageList NOTIFY imageListChanged)

public:
    explicit ClientMode(QObject *parent = nullptr);
    ~ClientMode();

    // 属性访问器
    bool isConnected() const { return m_connected; }
    QString serverUrl() const { return m_serverUrl; }
    QString connectionStatus() const { return m_connectionStatus; }
    QJsonArray imageList() const { return m_imageList; }

    // Qt可调用方法
    Q_INVOKABLE void connectToServer(const QString &host, int port);
    Q_INVOKABLE void disconnectFromServer();
    Q_INVOKABLE void loadImageList(const QString &path = "");
    Q_INVOKABLE QString getImageUrl(const QString &imagePath);
    Q_INVOKABLE QString getThumbnailUrl(const QString &imagePath);
    Q_INVOKABLE void testConnection();

    // 原有接口保持兼容
    void connectToServer(const std::string& address, int port);
    void enableAutoReconnect(bool enable);
    void discoverServers(std::function<void(const std::vector<std::string>&)> handler);

signals:
    void connectedChanged();
    void serverUrlChanged();
    void connectionStatusChanged();
    void imageListChanged();
    void connectionError(const QString &error);
    void imageListLoaded(const QJsonArray &images);

private slots:
    void onTestConnectionFinished();
    void onImageListFinished();
    void onNetworkError(QNetworkReply::NetworkError error);

private:
    void setConnected(bool connected);
    void setConnectionStatus(const QString &status);
    void updateImageList(const QJsonArray &images);
    QString formatServerUrl(const QString &host, int port);

    QNetworkAccessManager *m_networkManager;
    QString m_serverUrl;
    QString m_connectionStatus;
    QJsonArray m_imageList;
    bool m_connected;
    bool m_autoReconnect;

    // 当前请求跟踪
    QNetworkReply *m_currentTestReply;
    QNetworkReply *m_currentImageListReply;

    // 服务发现回调
    std::function<void(const std::vector<std::string>&)> m_discoveryHandler;
};