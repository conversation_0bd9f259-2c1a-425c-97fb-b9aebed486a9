# 多文件夹路径映射修复文档

## 问题描述
原有的网络共享功能只支持第一个监听文件夹，多个文件夹时无法正确访问。例如：
- 添加了两个文件夹：`C:/Users/<USER>/Pictures/Saved Pictures` 和 `E:/完成`
- 只能访问第一个文件夹的内容，第二个文件夹无法访问

## 解决方案

### 1. 路径映射逻辑重构

#### 客户端到服务器路径映射 (`mapClientPathToServer`)
- **根路径 `/`**: 映射到第一个监听文件夹
- **文件夹路径 `/Saved Pictures`**: 映射到 `C:/Users/<USER>/Pictures/Saved Pictures`
- **文件夹路径 `/完成`**: 映射到 `E:/完成`
- **子路径 `/Saved Pictures/子文件夹`**: 映射到 `C:/Users/<USER>/Pictures/Saved Pictures/子文件夹`

#### 服务器到客户端路径映射 (`mapServerPathToClient`)
- **`C:/Users/<USER>/Pictures/Saved Pictures`**: 映射到 `/Saved Pictures`
- **`E:/完成`**: 映射到 `/完成`
- **`C:/Users/<USER>/Pictures/Saved Pictures/子文件夹`**: 映射到 `/Saved Pictures/子文件夹`

### 2. 根目录处理
新增 `handleRootFolderList()` 方法：
- 当客户端请求根路径 `/` 时，返回所有监听文件夹作为虚拟文件夹
- 每个监听文件夹显示为其基础名称（如 `Saved Pictures`、`完成`）

### 3. API 访问示例
修复后的访问方式：

```
# 获取根目录（显示所有监听文件夹）
GET /api/images?path=/

# 访问特定文件夹
GET /api/images?path=/Saved Pictures
GET /api/images?path=/完成

# 访问图片
GET /api/image/Saved Pictures/wallhaven-qzwxjq.webp
GET /api/image/完成/图片.jpg

# 访问缩略图
GET /api/thumbnail/Saved Pictures/wallhaven-qzwxjq.webp
GET /api/thumbnail/完成/图片.jpg
```

### 4. 控制台输出优化
服务器启动时会显示每个监听文件夹的访问示例：

```
=== 网络访问地址 ===
本地访问: http://127.0.0.1:3333
局域网访问地址:
  http://192.168.1.100:3333
API示例:
  获取根目录: http://192.168.1.100:3333/api/images?path=/
  访问文件夹 'Saved Pictures': http://192.168.1.100:3333/api/images?path=/Saved Pictures
  获取图片: http://192.168.1.100:3333/api/image/Saved Pictures/图片名.jpg
  访问文件夹 '完成': http://192.168.1.100:3333/api/images?path=/完成
  获取图片: http://192.168.1.100:3333/api/image/完成/图片名.jpg
==================
```

## 技术实现细节

### 关键修改
1. **路径映射算法**: 使用文件夹基础名称作为客户端路径前缀
2. **根目录虚拟化**: 根路径返回所有监听文件夹的虚拟列表
3. **安全性保持**: 仍然只允许访问监听文件夹内的内容
4. **向后兼容**: 保持现有API结构不变

### 性能优化
- 使用 `qAsConst()` 包装Qt容器以避免隐式共享分离
- 路径映射算法时间复杂度为 O(n)，n为监听文件夹数量
- 缓存文件夹基础名称以提高性能

## 测试验证
1. 添加多个监听文件夹
2. 访问根路径验证文件夹列表
3. 访问各个文件夹验证内容
4. 测试图片和缩略图访问
5. 验证路径安全性检查
