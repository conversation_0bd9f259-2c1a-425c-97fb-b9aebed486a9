# 网络共享功能完整流程总结

## 🌐 **总体架构概述**

你的理解完全正确！整个系统通过隐藏真实服务器路径，只暴露客户端友好的虚拟路径来实现安全的网络共享。

## 📋 **完整流程详解**

### 1. **服务端初始化**

#### **1.1 监听文件夹配置**
```cpp
// 用户添加监听文件夹
networkBackend.addWatchFolder("C:/Users/<USER>/Pictures/Saved Pictures");
networkBackend.addWatchFolder("E:/完成");
```

#### **1.2 服务器启动**
```cpp
// 启动HTTP服务器
networkBackend.startImageServer(3333);
```

#### **1.3 控制台输出**
```
=== 网络访问地址 ===
本地访问: http://127.0.0.1:3333
局域网访问地址:
  http://192.168.1.100:3333
API示例:
  获取根目录: http://192.168.1.100:3333/api/images?path=/
  访问文件夹 'Saved Pictures': http://192.168.1.100:3333/api/images?path=/Saved Pictures
  获取图片: http://192.168.1.100:3333/api/image/Saved Pictures/图片名.jpg
  访问文件夹 '完成': http://192.168.1.100:3333/api/images?path=/完成
  获取图片: http://192.168.1.100:3333/api/image/完成/图片名.jpg
==================
```

### 2. **客户端连接流程**

#### **2.1 初始连接**
```
客户端请求: GET /api/images?path=/
服务端处理: handleRootFolderList()
```

#### **2.2 根目录响应**
```json
{
  "images": [],
  "folders": [
    {"name": "Saved Pictures", "path": "/Saved Pictures", "isFolder": true},
    {"name": "完成", "path": "/完成", "isFolder": true}
  ],
  "currentPath": "/",
  "folderCount": 2,
  "imageCount": 0
}
```

#### **2.3 前端显示**
- ImagePage显示两个文件夹图标：📁 Saved Pictures、📁 完成
- 用户看到的是虚拟路径结构，从根目录 `/` 开始
- 网络模式指示器显示：🌐 网络模式

### 3. **文件夹浏览流程**

#### **3.1 用户双击文件夹**
```qml
onDoubleClicked: {
    if (model.isFolder) {
        currentPath = model.filePath  // "/Saved Pictures"
        loadImagesFromFolder()
    }
}
```

#### **3.2 路径映射过程**
```
客户端路径: "/Saved Pictures"
↓ mapClientPathToServer()
服务器路径: "C:/Users/<USER>/Pictures/Saved Pictures"
↓ ImageProcessor.getNetworkImageList()
扫描真实文件夹内容
↓ mapServerPathToClient()
返回客户端路径格式
```

#### **3.3 文件夹内容响应**
```json
{
  "images": [
    {"name": "photo1.jpg", "path": "/Saved Pictures/photo1.jpg", "isFolder": false},
    {"name": "image2.png", "path": "/Saved Pictures/image2.png", "isFolder": false}
  ],
  "folders": [
    {"name": "子文件夹", "path": "/Saved Pictures/子文件夹", "isFolder": true}
  ],
  "currentPath": "/Saved Pictures"
}
```

### 4. **缩略图加载流程**

#### **4.1 网络模式URL构造**
```qml
function loadThumbnail() {
    if (isNetworkMode) {
        // 构造网络URL
        currentSource = networkBaseUrl + "/api/thumbnail" + model.filePath;
        // 例如: http://192.168.1.100:3333/api/thumbnail/Saved Pictures/photo1.jpg
    }
}
```

#### **4.2 服务端处理**
```cpp
// 接收请求: GET /api/thumbnail/Saved Pictures/photo1.jpg
QString clientPath = "/Saved Pictures/photo1.jpg";
QString serverPath = mapClientPathToServer(clientPath);
// serverPath = "C:/Users/<USER>/Pictures/Saved Pictures/photo1.jpg"
QByteArray thumbnailData = m_imageProcessor->getNetworkThumbnail(serverPath);
```

### 5. **错误处理与重试机制**

#### **5.1 加载失败处理**
```qml
onStatusChanged: {
    if (status === Image.Error) {
        errorPlaceholder.visible = true;
        retryButton.visible = true;  // 显示🔄重试按钮
    }
}
```

#### **5.2 用户主导重试**
```qml
MouseArea {
    onClicked: {
        retryButton.visible = false;  // 立即隐藏按钮
        imageCell.retryLoad();        // 执行重试
    }
}
```

#### **5.3 重试逻辑**
```qml
function retryLoad() {
    if (retryCount < maxRetries && !isRetrying) {
        isRetrying = true;
        retryCount++;
        Qt.callLater(function() {
            loadThumbnail();
            isRetrying = false;
        });
    }
}
```

### 6. **图片预览流程**

#### **6.1 双击图片**
```qml
onDoubleClicked: {
    if (!model.isFolder) {
        openImagePreview(index);  // 打开预览窗口
    }
}
```

#### **6.2 预览窗口网络模式**
```qml
function loadImageByIndex(index) {
    if (isNetworkMode) {
        let networkUrl = networkBackend.serverUrl + "/api/image" + imagePath;
        previewImage.source = networkUrl;
        // 例如: http://192.168.1.100:3333/api/image/Saved Pictures/photo1.jpg
    }
}
```

## 🔒 **安全机制**

### **路径隐藏与验证**
- ✅ 客户端只看到: `/Saved Pictures/photo1.jpg`
- ❌ 客户端看不到: `C:/Users/<USER>/Pictures/Saved Pictures/photo1.jpg`
- ✅ 服务端验证: 只允许访问监听文件夹内的内容

### **访问控制**
```cpp
bool isPathInWatchFolders(const QString& path) {
    for (const QString& watchFolder : m_watchFolders) {
        if (path.startsWith(watchFolder)) return true;
    }
    return false;
}
```

## 🎯 **关键优势**

1. **路径安全**: 完全隐藏服务器真实路径结构
2. **多文件夹支持**: 每个监听文件夹独立的虚拟命名空间
3. **统一体验**: 网络模式与本地模式UI完全一致
4. **智能切换**: 自动检测网络状态，无缝切换模式
5. **用户友好**: 透明的错误处理和重试机制
6. **性能优化**: 复用本地缓存系统，支持并发控制

## 📊 **API端点总结**

| 端点 | 客户端路径 | 服务端映射 | 功能 |
|------|------------|------------|------|
| `/api/images?path=/` | `/` | 虚拟根目录 | 列出所有监听文件夹 |
| `/api/images?path=/Saved Pictures` | `/Saved Pictures` | `C:/Users/<USER>/Saved Pictures` | 列出文件夹内容 |
| `/api/image/Saved Pictures/pic.jpg` | `/Saved Pictures/pic.jpg` | `C:/Users/<USER>/pic.jpg` | 获取完整图片 |
| `/api/thumbnail/完成/photo.png` | `/完成/photo.png` | `E:/完成/photo.png` | 获取缩略图 |

## 🔄 **完整工作流程**

1. **服务器启动** → 监听文件夹配置 → 输出访问地址
2. **客户端连接** → 请求根目录 → 显示虚拟文件夹列表
3. **文件夹浏览** → 双击进入 → 路径映射 → 内容加载
4. **缩略图显示** → 网络URL构造 → 服务端处理 → 图片返回
5. **错误处理** → 显示重试按钮 → 用户点击 → 重新加载
6. **图片预览** → 网络URL加载 → 全尺寸图片显示

这个设计完美实现了你的需求：服务端不直接暴露真实路径，客户端通过虚拟路径（如`/Saved Pictures/image.jpg`）来访问资源，既保证了安全性又提供了直观的用户体验。
