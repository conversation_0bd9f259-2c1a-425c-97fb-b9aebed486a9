[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 12, "durationMicroseconds": 243, "errorMessage": "", "functionName": "width", "line": 12}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 51, "errorMessage": "", "functionName": "height", "line": 12}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 50, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "previewWindows", "line": 17}, {"codegenSuccessful": false, "column": 59, "durationMicroseconds": 117, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "sortModeNames", "line": 18}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 1055, "errorMessage": "Cannot generate efficient code for MoveRegExp", "functionName": "onCurrentP<PERSON><PERSON><PERSON><PERSON>", "line": 24}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "getBreadcrumbParts", "line": 479}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 352, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "loadImagesFromFolder", "line": 503}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "openImagePreview", "line": 513}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "removePreviewWindow", "line": 532}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showFileNameTip", "line": 543}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 69, "errorMessage": "", "functionName": "showMaxWindowsMessage", "line": 550}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "_copyPathToClipboard", "line": 552}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showCopyTip", "line": 559}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "openConversionWindowWithFormat", "line": 566}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 586, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "requestVisibleThumbnails", "line": 590}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showSortTip", "line": 603}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 104, "errorMessage": "", "functionName": "width", "line": 34}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 237, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 35}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 76, "errorMessage": "", "functionName": "visible", "line": 35}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 34, "errorMessage": "", "functionName": "centerIn", "line": 37}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 75, "errorMessage": "", "functionName": "onTriggered", "line": 38}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 160, "errorMessage": "", "functionName": "width", "line": 43}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 139, "errorMessage": "", "functionName": "height", "line": 43}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 193, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 44}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 74, "errorMessage": "", "functionName": "visible", "line": 44}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 519, "errorMessage": "", "functionName": "onTextChanged", "line": 49}, {"codegenSuccessful": false, "column": 23, "durationMicroseconds": 142, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 45}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 37, "errorMessage": "", "functionName": "centerIn", "line": 47}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 88, "errorMessage": "", "functionName": "width", "line": 57}, {"codegenSuccessful": false, "column": 70, "durationMicroseconds": 202, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 57}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 71, "errorMessage": "", "functionName": "horizontalCenter", "line": 58}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 60, "errorMessage": "", "functionName": "bottom", "line": 58}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 36, "errorMessage": "", "functionName": "centerIn", "line": 60}, {"codegenSuccessful": true, "column": 64, "durationMicroseconds": 80, "errorMessage": "", "functionName": "onTriggered", "line": 61}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 45, "errorMessage": "", "functionName": "fill", "line": 67}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 64, "errorMessage": "", "functionName": "onClicked", "line": 75}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 62, "errorMessage": "", "functionName": "left", "line": 73}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 55, "errorMessage": "", "functionName": "verticalCenter", "line": 73}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 43, "errorMessage": "", "functionName": "height", "line": 80}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 54, "errorMessage": "", "functionName": "left", "line": 79}, {"codegenSuccessful": true, "column": 69, "durationMicroseconds": 54, "errorMessage": "", "functionName": "right", "line": 79}, {"codegenSuccessful": true, "column": 116, "durationMicroseconds": 58, "errorMessage": "", "functionName": "verticalCenter", "line": 79}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 56, "errorMessage": "", "functionName": "verticalCenter", "line": 82}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 238, "errorMessage": "Cannot access value for name fullPath", "functionName": "model", "line": 85}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 212, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 88}, {"codegenSuccessful": false, "column": 44, "durationMicroseconds": 215, "errorMessage": "Cannot access value for name modelData", "functionName": "bold", "line": 89}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 441, "errorMessage": "Cannot access value for name index", "functionName": "onCompleted", "line": 90}, {"codegenSuccessful": true, "column": 72, "durationMicroseconds": 69, "errorMessage": "", "functionName": "cursor<PERSON><PERSON>pe", "line": 92}, {"codegenSuccessful": true, "column": 112, "durationMicroseconds": 117, "errorMessage": "", "functionName": "acceptedButtons", "line": 92}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 1378, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 93}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 40, "errorMessage": "", "functionName": "onClicked", "line": 93}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 43, "errorMessage": "", "functionName": "fill", "line": 92}, {"codegenSuccessful": false, "column": 110, "durationMicroseconds": 212, "errorMessage": "Cannot access value for name index", "functionName": "visible", "line": 108}, {"codegenSuccessful": false, "column": 63, "durationMicroseconds": 152, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 114}, {"codegenSuccessful": false, "column": 69, "durationMicroseconds": 220, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 117}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 72, "errorMessage": "", "functionName": "contentWidth", "line": 122}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 73, "errorMessage": "", "functionName": "contentHeight", "line": 122}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 220, "errorMessage": "", "functionName": "isScrolling", "line": 123}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 218, "errorMessage": "", "functionName": "onIsScrollingChanged", "line": 125}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 408, "errorMessage": "method requestVisibleThumbnails cannot be resolved.", "functionName": "onContentYChanged", "line": 126}, {"codegenSuccessful": false, "column": 81, "durationMicroseconds": 360, "errorMessage": "method requestVisibleThumbnails cannot be resolved.", "functionName": "onHeightChanged", "line": 126}, {"codegenSuccessful": false, "column": 125, "durationMicroseconds": 330, "errorMessage": "method requestVisibleThumbnails cannot be resolved.", "functionName": "onWidthChanged", "line": 126}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 63, "errorMessage": "", "functionName": "fill", "line": 120}, {"codegenSuccessful": true, "column": 75, "durationMicroseconds": 235, "errorMessage": "", "functionName": "onTriggered", "line": 124}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 63, "errorMessage": "", "functionName": "policy", "line": 128}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 114, "errorMessage": "", "functionName": "visible", "line": 129}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 77, "errorMessage": "", "functionName": "onEntered", "line": 136}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 139, "errorMessage": "", "functionName": "onExited", "line": 136}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 34, "errorMessage": "", "functionName": "fill", "line": 135}, {"codegenSuccessful": true, "column": 72, "durationMicroseconds": 65, "errorMessage": "", "functionName": "radius", "line": 139}, {"codegenSuccessful": false, "column": 90, "durationMicroseconds": 97, "errorMessage": "Cannot load property pressed from (component in C:/Qt/file/palyer/compoment/imagePage.qml)::parent with type QQuickItem.", "functionName": "color", "line": 139}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 144, "errorMessage": "", "functionName": "width", "line": 143}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 261, "errorMessage": "", "functionName": "columns", "line": 145}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 159, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "model", "line": 147}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 192, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "currentSource", "line": 151}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 259, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 155}, {"codegenSuccessful": false, "column": 52, "durationMicroseconds": 95, "errorMessage": "Cannot access value for name imageGrid", "functionName": "preferredWidth", "line": 153}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 53, "errorMessage": "", "functionName": "preferredHeight", "line": 154}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 32, "errorMessage": "", "functionName": "fill", "line": 158}, {"codegenSuccessful": false, "column": 45, "durationMicroseconds": 71, "errorMessage": "Cannot load property fileNameHeight from (component in C:/Qt/file/palyer/compoment/imagePage.qml)::parent with type QQuickItem.", "functionName": "height", "line": 163}, {"codegenSuccessful": false, "column": 46, "durationMicroseconds": 107, "errorMessage": "Cannot access value for name model", "functionName": "opacity", "line": 164}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 67, "errorMessage": "", "functionName": "top", "line": 162}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 56, "errorMessage": "", "functionName": "left", "line": 162}, {"codegenSuccessful": true, "column": 90, "durationMicroseconds": 56, "errorMessage": "", "functionName": "right", "line": 162}, {"codegenSuccessful": false, "column": 50, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 169}, {"codegenSuccessful": true, "column": 105, "durationMicroseconds": 32, "errorMessage": "", "functionName": "centerIn", "line": 169}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 79, "errorMessage": "", "functionName": "pixelSize", "line": 170}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 347, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onEntered", "line": 176}, {"codegenSuccessful": false, "column": 55, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onExited", "line": 185}, {"codegenSuccessful": false, "column": 64, "durationMicroseconds": 320, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onPositionChanged", "line": 189}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 61, "errorMessage": "", "functionName": "onClicked", "line": 197}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 28, "errorMessage": "", "functionName": "onClicked", "line": 197}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 46, "errorMessage": "", "functionName": "fill", "line": 174}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 136, "errorMessage": "", "functionName": "fillMode", "line": 207}, {"codegenSuccessful": true, "column": 49, "durationMicroseconds": 106, "errorMessage": "", "functionName": "source", "line": 208}, {"codegenSuccessful": false, "column": 50, "durationMicroseconds": 48, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 209}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 119, "errorMessage": "", "functionName": "scale", "line": 210}, {"codegenSuccessful": true, "column": 58, "durationMicroseconds": 335, "errorMessage": "", "functionName": "onStatusChanged", "line": 213}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 73, "errorMessage": "", "functionName": "fill", "line": 206}, {"codegenSuccessful": true, "column": 107, "durationMicroseconds": 128, "errorMessage": "", "functionName": "type", "line": 211}, {"codegenSuccessful": false, "column": 54, "durationMicroseconds": 545, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "running", "line": 223}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 60, "errorMessage": "", "functionName": "visible", "line": 224}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 38, "errorMessage": "", "functionName": "centerIn", "line": 222}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 31, "errorMessage": "", "functionName": "fill", "line": 230}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 31, "errorMessage": "", "functionName": "centerIn", "line": 234}, {"codegenSuccessful": false, "column": 58, "durationMicroseconds": 752, "errorMessage": "Cannot access value for name model", "functionName": "onDoubleClicked", "line": 244}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 243}, {"codegenSuccessful": false, "column": 45, "durationMicroseconds": 76, "errorMessage": "Cannot load property fileNameHeight from (component in C:/Qt/file/palyer/compoment/imagePage.qml)::parent with type QQuickItem.", "functionName": "height", "line": 262}, {"codegenSuccessful": false, "column": 75, "durationMicroseconds": 174, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 262}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 80, "errorMessage": "", "functionName": "left", "line": 261}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 58, "errorMessage": "", "functionName": "right", "line": 261}, {"codegenSuccessful": true, "column": 95, "durationMicroseconds": 54, "errorMessage": "", "functionName": "bottom", "line": 261}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 266}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 57, "errorMessage": "", "functionName": "verticalAlignment", "line": 268}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 231, "errorMessage": "", "functionName": "horizontalAlignment", "line": 269}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 107, "errorMessage": "", "functionName": "textFormat", "line": 270}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 41, "errorMessage": "", "functionName": "wrapMode", "line": 271}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 36, "errorMessage": "", "functionName": "fill", "line": 265}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 69, "errorMessage": "", "functionName": "font", "line": 276}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 50, "errorMessage": "", "functionName": "text", "line": 277}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 154, "errorMessage": "", "functionName": "acceptedButtons", "line": 283}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 374, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onEntered", "line": 286}, {"codegenSuccessful": false, "column": 55, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onExited", "line": 295}, {"codegenSuccessful": false, "column": 64, "durationMicroseconds": 412, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onPositionChanged", "line": 298}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 62, "errorMessage": "", "functionName": "onClicked", "line": 306}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 30, "errorMessage": "", "functionName": "onClicked", "line": 306}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 40, "errorMessage": "", "functionName": "fill", "line": 282}, {"codegenSuccessful": false, "column": 41, "durationMicroseconds": 74, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "target", "line": 314}, {"codegenSuccessful": false, "column": 33, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onThumbnailReady", "line": 315}, {"codegenSuccessful": false, "column": 64, "durationMicroseconds": 464, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "running", "line": 329}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 125, "errorMessage": "", "functionName": "visible", "line": 329}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 53, "errorMessage": "", "functionName": "centerIn", "line": 329}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 164, "errorMessage": "", "functionName": "width", "line": 335}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 264, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 336}, {"codegenSuccessful": true, "column": 75, "durationMicroseconds": 82, "errorMessage": "", "functionName": "visible", "line": 336}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 88, "errorMessage": "", "functionName": "horizontalCenter", "line": 337}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 70, "errorMessage": "", "functionName": "bottom", "line": 337}, {"codegenSuccessful": true, "column": 99, "durationMicroseconds": 85, "errorMessage": "", "functionName": "bottom<PERSON>argin", "line": 337}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 70, "errorMessage": "", "functionName": "type", "line": 338}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 106, "errorMessage": "", "functionName": "verticalCenter", "line": 341}, {"codegenSuccessful": true, "column": 68, "durationMicroseconds": 67, "errorMessage": "", "functionName": "left", "line": 341}, {"codegenSuccessful": true, "column": 92, "durationMicroseconds": 39, "errorMessage": "", "functionName": "centerIn", "line": 345}, {"codegenSuccessful": false, "column": 50, "durationMicroseconds": 162, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 347}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 42, "errorMessage": "", "functionName": "fill", "line": 347}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 52, "errorMessage": "", "functionName": "type", "line": 348}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 964, "errorMessage": "Cannot generate efficient code for LoadElement with non-list base type QQuickItem::children with type QQuickItem (adjusted to QVariant, stored as QVariant)", "functionName": "onEntered", "line": 352}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 454, "errorMessage": "Cannot generate efficient code for LoadElement with non-list base type QQuickItem::children with type QQuickItem (adjusted to QVariant, stored as QVariant)", "functionName": "onExited", "line": 358}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 801, "errorMessage": "", "functionName": "onPositionChanged", "line": 359}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 368, "errorMessage": "Cannot access value for name currentPath", "functionName": "onClicked", "line": 365}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 351}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 36, "errorMessage": "", "functionName": "centerIn", "line": 375}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 205, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 379}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 36, "errorMessage": "", "functionName": "fill", "line": 378}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 55, "errorMessage": "", "functionName": "type", "line": 382}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 934, "errorMessage": "Cannot generate efficient code for LoadElement with non-list base type QQuickItem::children with type QQuickItem (adjusted to QVariant, stored as QVariant)", "functionName": "onEntered", "line": 386}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 211, "errorMessage": "Cannot generate efficient code for LoadElement with non-list base type QQuickItem::children with type QQuickItem (adjusted to QVariant, stored as QVariant)", "functionName": "onExited", "line": 393}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 847, "errorMessage": "", "functionName": "onPositionChanged", "line": 394}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 338, "errorMessage": "Cannot access value for name currentPath", "functionName": "onClicked", "line": 400}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 58, "errorMessage": "", "functionName": "fill", "line": 385}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 489, "errorMessage": "", "functionName": "width", "line": 408}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 118, "errorMessage": "", "functionName": "height", "line": 408}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 205, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 409}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 232, "errorMessage": "", "functionName": "visible", "line": 409}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 89, "errorMessage": "", "functionName": "type", "line": 410}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 89, "errorMessage": "", "functionName": "wrapMode", "line": 417}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 50, "errorMessage": "", "functionName": "centerIn", "line": 414}, {"codegenSuccessful": false, "column": 33, "durationMicroseconds": 26, "errorMessage": "Cannot access value for name mouseInsidePage", "functionName": "enabled", "line": 425}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 170, "errorMessage": "", "functionName": "onActivated", "line": 426}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 776, "errorMessage": "Cannot access value for name sortMode", "functionName": "onActivated", "line": 431}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 1223, "errorMessage": "", "functionName": "currentFolder", "line": 442}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 236, "errorMessage": "Cannot find name currentPath", "functionName": "onAccepted", "line": 443}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 106, "errorMessage": "", "functionName": "standardButtons", "line": 452}, {"codegenSuccessful": true, "column": 81, "durationMicroseconds": 52, "errorMessage": "", "functionName": "centerIn", "line": 452}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 58, "errorMessage": "", "functionName": "width", "line": 454}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 50, "errorMessage": "", "functionName": "wrapMode", "line": 454}, {"codegenSuccessful": false, "column": 19, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name maxPreviewWindows", "functionName": "text", "line": 455}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 18, "errorMessage": "Cannot find name mouseInsidePage", "functionName": "onEntered", "line": 464}, {"codegenSuccessful": false, "column": 19, "durationMicroseconds": 13, "errorMessage": "Cannot find name mouseInsidePage", "functionName": "onExited", "line": 465}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 37, "errorMessage": "", "functionName": "fill", "line": 462}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 604, "errorMessage": "", "functionName": "onCompleted", "line": 468}], "filePath": "C:/Qt/file/palyer/compoment/imagePage.qml"}], "moduleId": "palyer(palyer)"}]