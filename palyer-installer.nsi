﻿; Palyer 图片浏览器安装脚本
; 基于检测到的Release文件夹结构

!define APP_NAME "Palyer"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Palyer Team"
!define APP_EXE "palyer.exe"
!define APP_DESCRIPTION "专业的图片浏览器，支持多种格式和网络共享"
!define SOURCE_DIR "build\Desktop_Qt_6_9_1_MinGW_64_bit-Release"

; 现代UI
!include "MUI2.nsh"

; 基本设置
Name "${APP_NAME}"
OutFile "Palyer-${APP_VERSION}-Setup.exe"
InstallDir "$PROGRAMFILES64\${APP_NAME}"
RequestExecutionLevel admin

; 界面设置
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

; 页面配置
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; 语言
!insertmacro MUI_LANGUAGE "SimpChinese"

; 版本信息
VIProductVersion "*******"
VIAddVersionKey "ProductName" "${APP_NAME}"
VIAddVersionKey "ProductVersion" "${APP_VERSION}"
VIAddVersionKey "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey "FileDescription" "${APP_DESCRIPTION}"
VIAddVersionKey "FileVersion" "${APP_VERSION}"

; 安装组件
Section "核心程序" SEC01
    SectionIn RO  ; 必需组件
    SetOutPath "$INSTDIR"
    
    ; 主程序文件
    File "${SOURCE_DIR}\${APP_EXE}"
    File "${SOURCE_DIR}\libbackend.dll"
    
    ; Qt核心库
    File "${SOURCE_DIR}\Qt6Core.dll"
    File "${SOURCE_DIR}\Qt6Gui.dll"
    File "${SOURCE_DIR}\Qt6Quick.dll"
    File "${SOURCE_DIR}\Qt6Qml.dll"
    File "${SOURCE_DIR}\Qt6QmlCore.dll"
    File "${SOURCE_DIR}\Qt6QmlMeta.dll"
    File "${SOURCE_DIR}\Qt6QmlModels.dll"
    File "${SOURCE_DIR}\Qt6QmlWorkerScript.dll"
    File "${SOURCE_DIR}\Qt6Network.dll"
    File "${SOURCE_DIR}\Qt6OpenGL.dll"
    
    ; Qt Quick相关
    File "${SOURCE_DIR}\Qt6QuickControls2.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2Basic.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2BasicStyleImpl.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2Impl.dll"
    File "${SOURCE_DIR}\Qt6QuickDialogs2.dll"
    File "${SOURCE_DIR}\Qt6QuickDialogs2QuickImpl.dll"
    File "${SOURCE_DIR}\Qt6QuickDialogs2Utils.dll"
    File "${SOURCE_DIR}\Qt6QuickEffects.dll"
    File "${SOURCE_DIR}\Qt6QuickLayouts.dll"
    File "${SOURCE_DIR}\Qt6QuickShapes.dll"
    File "${SOURCE_DIR}\Qt6QuickTemplates2.dll"
    
    ; 多媒体支持
    File "${SOURCE_DIR}\Qt6Multimedia.dll"
    File "${SOURCE_DIR}\Qt6MultimediaQuick.dll"
    
    ; 其他Qt库
    File "${SOURCE_DIR}\Qt6ShaderTools.dll"
    File "${SOURCE_DIR}\Qt6Svg.dll"
    File "${SOURCE_DIR}\Qt6Widgets.dll"
    
    ; 实验室模块
    File "${SOURCE_DIR}\Qt6LabsFolderListModel.dll"
    File "${SOURCE_DIR}\Qt6LabsPlatform.dll"
    File "${SOURCE_DIR}\Qt6LabsQmlModels.dll"
    
    ; MinGW运行时
    File "${SOURCE_DIR}\libgcc_s_seh-1.dll"
    File "${SOURCE_DIR}\libstdc++-6.dll"
    File "${SOURCE_DIR}\libwinpthread-1.dll"
    
    ; FFmpeg库
    File "${SOURCE_DIR}\avcodec-61.dll"
    File "${SOURCE_DIR}\avformat-61.dll"
    File "${SOURCE_DIR}\avutil-59.dll"
    File "${SOURCE_DIR}\swresample-5.dll"
    File "${SOURCE_DIR}\swscale-8.dll"
    
    ; 其他依赖
    File "${SOURCE_DIR}\D3Dcompiler_47.dll"
    File "${SOURCE_DIR}\opengl32sw.dll"
    
    ; 图像处理工具
    File "${SOURCE_DIR}\ffmpeg.exe"
    File "${SOURCE_DIR}\avifdec.exe"
    File "${SOURCE_DIR}\cwebp.exe"
    File "${SOURCE_DIR}\dwebp.exe"
    
    ; 平台插件
    SetOutPath "$INSTDIR\platforms"
    File "${SOURCE_DIR}\platforms\qwindows.dll"
    
    ; 图像格式插件
    SetOutPath "$INSTDIR\imageformats"
    File /r "${SOURCE_DIR}\imageformats\*"
    
    ; 多媒体插件
    SetOutPath "$INSTDIR\multimedia"
    File /r "${SOURCE_DIR}\multimedia\*"
    
    ; 网络信息插件
    SetOutPath "$INSTDIR\networkinformation"
    File /r "${SOURCE_DIR}\networkinformation\*"
    
    ; TLS插件
    SetOutPath "$INSTDIR\tls"
    File /r "${SOURCE_DIR}\tls\*"
    
    ; 样式插件
    SetOutPath "$INSTDIR\styles"
    File /r "${SOURCE_DIR}\styles\*"
    
    ; 图标引擎
    SetOutPath "$INSTDIR\iconengines"
    File /r "${SOURCE_DIR}\iconengines\*"
    
    ; QML模块
    SetOutPath "$INSTDIR\qml"
    File /r "${SOURCE_DIR}\qml\*"
    
    ; 应用QML文件
    SetOutPath "$INSTDIR\palyer"
    File /r "${SOURCE_DIR}\palyer\*"
    
    ; 创建快捷方式
    CreateDirectory "$SMPROGRAMS\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\卸载 ${APP_NAME}.lnk" "$INSTDIR\uninstall.exe"
    CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"
    
    ; 注册表信息
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\uninstall.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayIcon" "$INSTDIR\${APP_EXE}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoRepair" 1
    
    ; 创建卸载程序
    WriteUninstaller "$INSTDIR\uninstall.exe"
SectionEnd

Section "可选样式" SEC02
    SetOutPath "$INSTDIR"
    
    ; 额外的样式库
    File "${SOURCE_DIR}\Qt6QuickControls2Fusion.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2FusionStyleImpl.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2Imagine.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2ImagineStyleImpl.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2Material.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2MaterialStyleImpl.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2Universal.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2UniversalStyleImpl.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2WindowsStyleImpl.dll"
    File "${SOURCE_DIR}\Qt6QuickControls2FluentWinUI3StyleImpl.dll"
SectionEnd

Section "虚拟键盘支持" SEC03
    SetOutPath "$INSTDIR"
    File "${SOURCE_DIR}\Qt6VirtualKeyboard.dll"
    
    SetOutPath "$INSTDIR\platforminputcontexts"
    File /r "${SOURCE_DIR}\platforminputcontexts\*"
SectionEnd

Section "调试工具" SEC04
    SetOutPath "$INSTDIR\qmltooling"
    File /r "${SOURCE_DIR}\qmltooling\*"
SectionEnd

; 组件描述
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "Palyer核心程序和必需的运行时库"
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "额外的UI样式主题（Material、Fusion等）"
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC03} "虚拟键盘支持（触屏设备）"
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC04} "QML调试工具（开发者使用）"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; 卸载程序
Section "Uninstall"
    ; 删除文件和文件夹
    RMDir /r "$INSTDIR"
    
    ; 删除快捷方式
    Delete "$DESKTOP\${APP_NAME}.lnk"
    RMDir /r "$SMPROGRAMS\${APP_NAME}"
    
    ; 删除注册表项
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
SectionEnd

; 安装前检查
Function .onInit
    ; 检查是否已安装
    ReadRegStr $R0 HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString"
    StrCmp $R0 "" done
    
    MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
    "${APP_NAME} 已经安装。$\n$\n点击 '确定' 卸载之前的版本，或点击 '取消' 退出安装。" \
    IDOK uninst
    Abort
    
    uninst:
        ClearErrors
        ExecWait '$R0 /S _?=$INSTDIR'
        
        IfErrors no_remove_uninstaller done
        no_remove_uninstaller:
    
    done:
FunctionEnd
