{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_deferred_aotstats_setup"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake:1103:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 1, "file": 1, "line": 1, "parent": 1}, {"command": 0, "file": 0, "line": 1210, "parent": 2}]}, "dependencies": [{"id": "module_palyer_aotstats_target::@6890427a1f51a3e7e1df"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "all_aotstats::@6890427a1f51a3e7e1df", "name": "all_aotstats", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/CMakeFiles/all_aotstats", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/60efe0b784417ad25bcf545a28e72a55/all_aotstats.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/7edb533fa42cf3d6641d1bf560752cbd/all_aotstats.aotstats.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/7edb533fa42cf3d6641d1bf560752cbd/module_palyer.aotstats.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/7edb533fa42cf3d6641d1bf560752cbd/palyer_Main_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/AppState_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/ImagePreviewWindow_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/NetworkSettingsDialog_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/SettingsPage_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/e62c4caedb4dcbba341fee5226c33303/imagePage_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/89d2c1d9975736725dd82f98ce51f0bc/BaseWindowTemplate_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/99284a222e026054a1227c2ee3702b47/ConversionWindow_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/2028906c1413333e401fcd3264098cea/palyer_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}