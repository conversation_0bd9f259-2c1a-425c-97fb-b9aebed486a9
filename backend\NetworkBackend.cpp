#include "NetworkBackend.h"
#include "ImageProcessor.h"
#include <QNetworkInterface>
#include <QHostAddress>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QFileInfo>
#include <QDir>
#include <QUrl>
#include <QMutexLocker>
#include <QRegularExpression>
#include <QTextStream>
#include <QDateTime>
#include <QMimeDatabase>
#include <QTimer>
#include <QQueue>
#include <QDebug>
#include <QThread>
#include <QCryptographicHash>
#include <QRandomGenerator>

NetworkBackend::NetworkBackend(QObject* parent) : QObject(parent) {
    refreshLocalAddresses();

    // 初始化重试定时器
    m_retryTimer = new QTimer(this);
    m_retryTimer->setSingleShot(true);
    m_retryTimer->setInterval(1000); // 1秒后处理重试
    connect(m_retryTimer, &QTimer::timeout, this, &NetworkBackend::processRetryQueue);
}

NetworkBackend::~NetworkBackend() {
    stopImageServer();
}

void NetworkBackend::refreshLocalAddresses() {
    QStringList v4;
    QString v6first;
    for (const QNetworkInterface& iface : QNetworkInterface::allInterfaces()) {
        if (!(iface.flags() & QNetworkInterface::IsUp) ||
            (iface.flags() & QNetworkInterface::IsLoopBack) ||
            (iface.flags() & QNetworkInterface::IsPointToPoint) ||
            iface.humanReadableName().contains("VMware", Qt::CaseInsensitive) ||
            iface.humanReadableName().contains("VirtualBox", Qt::CaseInsensitive) ||
            iface.humanReadableName().contains("Loopback", Qt::CaseInsensitive) ||
            iface.humanReadableName().contains("Bluetooth", Qt::CaseInsensitive))
            continue;
        for (const QNetworkAddressEntry& entry : iface.addressEntries()) {
            QHostAddress addr = entry.ip();
            if (addr.protocol() == QAbstractSocket::IPv4Protocol) {
                QString ip = addr.toString();
                if (ip.startsWith("192.")) {
                    v4 << ip;
                } else if (!ip.startsWith("10.") && !(ip.startsWith("172."))) {
                    v4 << ip;
                }
            } else if (addr.protocol() == QAbstractSocket::IPv6Protocol &&
                       !addr.isLoopback() && !addr.isLinkLocal() && v6first.isEmpty()) {
                v6first = addr.toString();
            }
        }
    }
    QStringList v6;
    if (!v6first.isEmpty()) v6 << v6first;
    
    if (m_ipv4Addresses != v4 || m_ipv6Addresses != v6) {
        m_ipv4Addresses = v4;
        m_ipv6Addresses = v6;
        emit localAddressesChanged();
    }
}

bool NetworkBackend::startImageServer(int port) {
    QMutexLocker locker(&m_serverMutex);
    
    if (m_tcpServer && m_tcpServer->isListening()) {
        qWarning() << "Server already running on port" << m_currentPort;
        return false;
    }
    
    if (!m_imageProcessor) {
        qWarning() << "ImageProcessor not set";
        return false;
    }
    
    m_tcpServer = new QTcpServer(this);
    connect(m_tcpServer, &QTcpServer::newConnection, this, &NetworkBackend::onNewConnection);
    
    if (m_tcpServer->listen(QHostAddress::Any, port)) {
        m_currentPort = m_tcpServer->serverPort();
        qInfo() << "Image server started on port" << m_currentPort;

        // 输出访问地址信息
        qInfo() << "=== 网络访问地址 ===";
        qInfo() << "本地访问: http://127.0.0.1:" << m_currentPort;
        qInfo() << "本地访问: http://localhost:" << m_currentPort;

        // 输出所有可用的网络接口地址
        if (!m_ipv4Addresses.isEmpty()) {
            qInfo() << "局域网访问地址:";
            for (const QString& ip : std::as_const(m_ipv4Addresses)) {
                qInfo() << "  http://" << ip << ":" << m_currentPort;
            }
        }

        if (!m_ipv6Addresses.isEmpty()) {
            qInfo() << "IPv6访问地址:";
            for (const QString& ip : std::as_const(m_ipv6Addresses)) {
                qInfo() << "  http://[" << ip << "]:" << m_currentPort;
            }
        }

        qInfo() << "API示例:";
        QString exampleIP = m_ipv4Addresses.isEmpty() ? "127.0.0.1" : m_ipv4Addresses.first();
        qInfo() << "  获取根目录: http://" << exampleIP << ":" << m_currentPort << "/api/images?path=/";

        // 显示每个监听文件夹的访问示例
        if (!m_watchFolders.isEmpty()) {
            for (const QString& watchFolder : qAsConst(m_watchFolders)) {
                QFileInfo folderInfo(watchFolder);
                QString folderName = folderInfo.baseName();
                qInfo() << "  访问文件夹 '" << folderName << "': http://" << exampleIP << ":" << m_currentPort << "/api/images?path=/" << folderName;
                qInfo() << "  获取图片: http://" << exampleIP << ":" << m_currentPort << "/api/image/" << folderName << "/图片名.jpg";
            }
        }
        qInfo() << "==================";

        emit serverStatusChanged();
        return true;
    } else {
        qWarning() << "Failed to start server:" << m_tcpServer->errorString();
        delete m_tcpServer;
        m_tcpServer = nullptr;
        return false;
    }
}

void NetworkBackend::stopImageServer() {
    QMutexLocker locker(&m_serverMutex);
    
    if (m_tcpServer) {
        qInfo() << "Stopping image server...";
        
        // 关闭所有连接
        for (auto it = m_pendingRequests.begin(); it != m_pendingRequests.end(); ++it) {
            it.key()->close();
        }
        m_pendingRequests.clear();
        
        m_tcpServer->close();
        delete m_tcpServer;
        m_tcpServer = nullptr;
        m_currentPort = 0;
        
        // 等待活跃请求完成
        int waitCount = 0;
        while (m_activeRequests.loadRelaxed() > 0 && waitCount < 30) {
            QThread::msleep(100);
            waitCount++;
        }
        
        m_activeRequests.storeRelaxed(0);
        qInfo() << "Image server stopped";
        emit serverStatusChanged();
        emit requestStatsChanged();
    }
}

QString NetworkBackend::serverUrl() const {
    if (!m_tcpServer || !m_tcpServer->isListening()) {
        return QString();
    }
    
    QString ip = m_ipv4Addresses.isEmpty() ? "localhost" : m_ipv4Addresses.first();
    return QString("http://%1:%2").arg(ip).arg(m_currentPort);
}

void NetworkBackend::onNewConnection() {
    while (m_tcpServer && m_tcpServer->hasPendingConnections()) {
        QTcpSocket* socket = m_tcpServer->nextPendingConnection();
        connect(socket, &QTcpSocket::readyRead, this, &NetworkBackend::onSocketReadyRead);
        connect(socket, &QTcpSocket::disconnected, this, &NetworkBackend::onSocketDisconnected);
        
        m_pendingRequests[socket] = QByteArray();
        qDebug() << "New connection from" << socket->peerAddress().toString();
    }
}

void NetworkBackend::onSocketReadyRead() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) return;
    
    QByteArray data = socket->readAll();
    m_pendingRequests[socket].append(data);
    
    // 检查是否收到完整的HTTP请求
    QByteArray& requestData = m_pendingRequests[socket];
    if (requestData.contains("\r\n\r\n")) {
        processHttpRequest(socket, requestData);
        m_pendingRequests.remove(socket);
    }
}

void NetworkBackend::onSocketDisconnected() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) return;
    
    m_pendingRequests.remove(socket);
    socket->deleteLater();
}

void NetworkBackend::processHttpRequest(QTcpSocket* socket, const QByteArray& requestData) {
    m_activeRequests.fetchAndAddRelaxed(1);
    emit requestStatsChanged();
    
    QString method = parseHttpMethod(requestData);
    QString path = parseHttpPath(requestData);
    QHash<QString, QString> headers = parseHttpHeaders(requestData);
    
    qDebug() << "HTTP Request:" << method << path;
    
    QByteArray response;
    
    if (method == "OPTIONS") {
        response = createHttpResponse(200, "text/plain", QByteArray(), {
            {"Access-Control-Allow-Origin", "*"},
            {"Access-Control-Allow-Methods", "GET, OPTIONS"},
            {"Access-Control-Allow-Headers", "Range, Content-Type"}
        });
    } else if (method == "GET") {
        if (path.startsWith("/api/images")) {
            QUrl url(path);
            QUrlQuery query(url);
            QString clientPath = query.queryItemValue("path");
            response = handleImageList(clientPath, {});
        } else if (path.startsWith("/api/image/")) {
            QString clientPath = path.mid(11); // 移除 "/api/image/"
            clientPath = QUrl::fromPercentEncoding(clientPath.toUtf8());

            qDebug() << "Client image path:" << clientPath;

            // 确保路径以 "/" 开头
            if (!clientPath.startsWith("/")) {
                clientPath = "/" + clientPath;
            }

            // 映射客户端路径到服务器路径
            QString serverPath = mapClientPathToServer(clientPath);
            qDebug() << "Mapped to server path:" << serverPath;

            response = handleImageData(serverPath, headers);
        } else if (path.startsWith("/api/thumbnail/")) {
            QString clientPath = path.mid(15); // 移除 "/api/thumbnail/"
            clientPath = QUrl::fromPercentEncoding(clientPath.toUtf8());

            qDebug() << "Client thumbnail path:" << clientPath;

            // 确保路径以 "/" 开头
            if (!clientPath.startsWith("/")) {
                clientPath = "/" + clientPath;
            }

            // 映射客户端路径到服务器路径
            QString serverPath = mapClientPathToServer(clientPath);
            qDebug() << "Mapped to server path:" << serverPath;

            response = handleThumbnail(serverPath);
        } else {
            response = createErrorResponse("Not Found", 404);
        }
    } else {
        response = createErrorResponse("Method Not Allowed", 405);
    }
    
    socket->write(response);
    socket->flush();
    socket->close();
    
    m_activeRequests.fetchAndSubRelaxed(1);
    emit requestStatsChanged();
}

QString NetworkBackend::parseHttpMethod(const QByteArray& requestData) {
    int spaceIndex = requestData.indexOf(' ');
    if (spaceIndex > 0) {
        return QString::fromLatin1(requestData.left(spaceIndex));
    }
    return QString();
}

QString NetworkBackend::parseHttpPath(const QByteArray& requestData) {
    int firstSpace = requestData.indexOf(' ');
    int secondSpace = requestData.indexOf(' ', firstSpace + 1);
    if (firstSpace > 0 && secondSpace > firstSpace) {
        return QString::fromLatin1(requestData.mid(firstSpace + 1, secondSpace - firstSpace - 1));
    }
    return QString();
}

QHash<QString, QString> NetworkBackend::parseHttpHeaders(const QByteArray& requestData) {
    QHash<QString, QString> headers;
    QStringList lines = QString::fromLatin1(requestData).split("\r\n");
    
    for (int i = 1; i < lines.size(); ++i) {
        const QString& line = lines[i];
        if (line.isEmpty()) break;
        
        int colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
            QString key = line.left(colonIndex).trimmed().toLower();
            QString value = line.mid(colonIndex + 1).trimmed();
            headers[key] = value;
        }
    }
    
    return headers;
}

QByteArray NetworkBackend::createHttpResponse(int statusCode, const QString& contentType, 
                                            const QByteArray& body, const QHash<QString, QString>& headers) {
    QByteArray response;
    QTextStream stream(&response);
    
    // 状态行
    stream << "HTTP/1.1 " << statusCode << " ";
    switch (statusCode) {
        case 200: stream << "OK"; break;
        case 206: stream << "Partial Content"; break;
        case 400: stream << "Bad Request"; break;
        case 404: stream << "Not Found"; break;
        case 405: stream << "Method Not Allowed"; break;
        case 500: stream << "Internal Server Error"; break;
        default: stream << "Unknown"; break;
    }
    stream << "\r\n";
    
    // 基本头部
    stream << "Content-Type: " << contentType << "\r\n";
    stream << "Content-Length: " << body.size() << "\r\n";
    stream << "Connection: close\r\n";
    
    // 自定义头部
    for (auto it = headers.begin(); it != headers.end(); ++it) {
        stream << it.key() << ": " << it.value() << "\r\n";
    }
    
    stream << "\r\n";
    stream.flush();
    
    response.append(body);
    return response;
}

QByteArray NetworkBackend::handleImageList(const QString& clientPath, const QHash<QString, QString>& params) {
    Q_UNUSED(params)

    // 处理根路径请求 - 返回所有监听文件夹作为虚拟文件夹
    if (clientPath == "/" || clientPath.isEmpty()) {
        return handleRootFolderList();
    }

    // 映射客户端路径到服务器路径
    QString serverPath = mapClientPathToServer(clientPath);
    if (serverPath.isEmpty()) {
        return createErrorResponse("Invalid path mapping", 400);
    }

    // 验证文件夹路径
    if (!isValidFolderPath(serverPath)) {
        return createErrorResponse("Invalid folder path", 400);
    }

    // 安全检查：只允许访问监听文件夹内的路径
    if (!isPathInWatchFolders(serverPath)) {
        qWarning() << "Access denied for folder path outside watch folders:" << serverPath;
        return createErrorResponse("Access denied", 403);
    }

    QJsonObject result = m_imageProcessor->getNetworkImageList(serverPath);

    // 分离图片和文件夹，并映射路径
    QJsonArray images;
    QJsonArray folders;

    if (result.contains("images")) {
        QJsonArray allItems = result["images"].toArray();
        for (int i = 0; i < allItems.size(); ++i) {
            QJsonObject itemObj = allItems[i].toObject();
            QString serverPath = itemObj["path"].toString();
            itemObj["path"] = mapServerPathToClient(serverPath);

            if (itemObj["isFolder"].toBool()) {
                // 这是文件夹
                folders.append(itemObj);
            } else {
                // 这是图片文件
                images.append(itemObj);
            }
        }
    }

    // 重新构建结果
    QJsonObject finalResult;
    finalResult["images"] = images;
    finalResult["folders"] = folders;
    finalResult["currentPath"] = clientPath;
    finalResult["totalCount"] = images.size() + folders.size();
    finalResult["imageCount"] = images.size();
    finalResult["folderCount"] = folders.size();
    finalResult["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return createHttpResponse(200, "application/json", QJsonDocument(finalResult).toJson(), {
        {"Access-Control-Allow-Origin", "*"}
    });
}

QByteArray NetworkBackend::handleRootFolderList() {
    QJsonArray folders;

    // 为每个监听文件夹创建虚拟文件夹条目
    for (const QString& watchFolder : qAsConst(m_watchFolders)) {
        QFileInfo folderInfo(watchFolder);
        if (folderInfo.exists() && folderInfo.isDir()) {
            QJsonObject folderObj;
            folderObj["name"] = folderInfo.baseName();
            folderObj["path"] = "/" + folderInfo.baseName();
            folderObj["isFolder"] = true;
            folderObj["size"] = 0;
            folderObj["lastModified"] = folderInfo.lastModified().toString(Qt::ISODate);
            folders.append(folderObj);
        }
    }

    QJsonObject result;
    result["images"] = QJsonArray();
    result["folders"] = folders;
    result["currentPath"] = "/";
    result["totalCount"] = folders.size();
    result["imageCount"] = 0;
    result["folderCount"] = folders.size();
    result["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return createHttpResponse(200, "application/json", QJsonDocument(result).toJson(), {
        {"Access-Control-Allow-Origin", "*"}
    });
}

QByteArray NetworkBackend::handleImageData(const QString& imagePath, const QHash<QString, QString>& headers) {
    if (!isValidImagePath(imagePath)) {
        return createErrorResponse("Invalid image path", 400);
    }

    // 安全检查：只允许访问监听文件夹内的文件
    if (!isPathInWatchFolders(imagePath)) {
        qWarning() << "Access denied for path outside watch folders:" << imagePath;
        return createErrorResponse("Access denied", 403);
    }

    QFileInfo fileInfo(imagePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        return createErrorResponse("File not found", 404);
    }

    QFile file(imagePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return createErrorResponse("Cannot read file", 500);
    }

    QByteArray imageData = file.readAll();
    file.close();

    // 处理Range请求
    QString rangeHeader = headers.value("range");
    if (!rangeHeader.isEmpty() && rangeHeader.startsWith("bytes=")) {
        QStringList rangeParts = rangeHeader.mid(6).split("-");
        if (rangeParts.size() == 2) {
            qint64 start = rangeParts[0].toLongLong();
            qint64 end = rangeParts[1].isEmpty() ? imageData.size() - 1 : rangeParts[1].toLongLong();

            if (start < imageData.size() && end < imageData.size() && start <= end) {
                QByteArray rangeData = imageData.mid(start, end - start + 1);

                return createHttpResponse(206, "image/" + fileInfo.suffix().toLower(), rangeData, {
                    {"Content-Range", QString("bytes %1-%2/%3").arg(start).arg(end).arg(imageData.size())},
                    {"Accept-Ranges", "bytes"},
                    {"Access-Control-Allow-Origin", "*"}
                });
            }
        }
    }

    // 完整文件响应
    return createHttpResponse(200, "image/" + fileInfo.suffix().toLower(), imageData, {
        {"Accept-Ranges", "bytes"},
        {"Access-Control-Allow-Origin", "*"},
        {"Cache-Control", "public, max-age=3600"}
    });
}

QByteArray NetworkBackend::handleThumbnail(const QString& imagePath) {
    if (!isValidImagePath(imagePath)) {
        return createErrorResponse("Invalid image path", 400);
    }

    // 安全检查：只允许访问监听文件夹内的文件
    if (!isPathInWatchFolders(imagePath)) {
        qWarning() << "Access denied for thumbnail path outside watch folders:" << imagePath;
        return createErrorResponse("Access denied", 403);
    }

    QByteArray thumbnailData = m_imageProcessor->getNetworkThumbnail(imagePath);

    if (thumbnailData.isEmpty()) return createErrorResponse("Thumbnail not available", 404);

    return createHttpResponse(200, "image/jpeg", thumbnailData, {
        {"Access-Control-Allow-Origin", "*"},
        {"Cache-Control", "public, max-age=86400"}
    });
}

QByteArray NetworkBackend::createErrorResponse(const QString &error, int statusCode) {
    QJsonObject errorObj;
    errorObj["error"] = error;
    errorObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return createHttpResponse(statusCode, "application/json", QJsonDocument(errorObj).toJson(), {
        {"Access-Control-Allow-Origin", "*"}
    });
}

QString NetworkBackend::extractPathFromUrl(const QString &urlPath, const QString &prefix) {
    if (!urlPath.startsWith(prefix)) {
        return QString();
    }

    QString path = urlPath.mid(prefix.length());
    return QUrl::fromPercentEncoding(path.toUtf8());
}

bool NetworkBackend::isValidImagePath(const QString &path) {
    qDebug() << "Validating image path:" << path;

    if (path.isEmpty() || path.contains("..") || path.contains("//")) {
        qDebug() << "Path validation failed: empty or contains dangerous characters";
        return false;
    }

    QFileInfo fileInfo(path);
    if (!fileInfo.exists()) {
        qDebug() << "Path validation failed: file does not exist";
        return false;
    }

    if (!fileInfo.isFile()) {
        qDebug() << "Path validation failed: not a file";
        return false;
    }

    static const QStringList imageExtensions = {
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "avif", "tiff"
    };

    QString extension = fileInfo.suffix().toLower();
    bool isValidExtension = imageExtensions.contains(extension);
    qDebug() << "Extension:" << extension << "Valid:" << isValidExtension;

    return isValidExtension;
}

bool NetworkBackend::isValidFolderPath(const QString& path) {
    if (path.isEmpty() || path.contains("..") || path.contains("//")) return false;

    QFileInfo fileInfo(path);
    if (!fileInfo.exists() || !fileInfo.isDir()) return false;

    return true;
}

void NetworkBackend::addWatchFolder(const QString& folderPath) {
    QMutexLocker locker(&m_serverMutex);

    if (!folderPath.isEmpty() && !m_watchFolders.contains(folderPath)) {
        QFileInfo info(folderPath);
        if (info.exists() && info.isDir()) {
            m_watchFolders.append(folderPath);
            qInfo() << "Added watch folder:" << folderPath;
        } else {
            qWarning() << "Invalid folder path:" << folderPath;
        }
    }
}

void NetworkBackend::removeWatchFolder(const QString& folderPath) {
    QMutexLocker locker(&m_serverMutex);
    if (m_watchFolders.removeOne(folderPath)) qInfo() << "Removed watch folder:" << folderPath;
}

bool NetworkBackend::isPathInWatchFolders(const QString& path) {
    if (m_watchFolders.isEmpty()) return false;

    QFileInfo fileInfo(path);
    QString canonicalPath = fileInfo.canonicalFilePath();

    for (const QString& watchFolder : std::as_const(m_watchFolders)) {
        QFileInfo watchInfo(watchFolder);
        QString canonicalWatchPath = watchInfo.canonicalFilePath();

        if (canonicalPath.startsWith(canonicalWatchPath)) return true;
    }

    return false;
}



QString NetworkBackend::mapClientPathToServer(const QString& clientPath) {
    if (m_watchFolders.isEmpty()) return QString();

    // 根路径 "/" 返回第一个监听文件夹
    if (clientPath == "/" || clientPath.isEmpty()) {
        return m_watchFolders.first();
    }

    // 确保客户端路径以 "/" 开头
    QString normalizedClientPath = clientPath;
    if (!normalizedClientPath.startsWith("/")) {
        normalizedClientPath = "/" + normalizedClientPath;
    }

    // 遍历所有监听文件夹，找到匹配的文件夹名
    for (const QString& watchFolder : qAsConst(m_watchFolders)) {
        QFileInfo folderInfo(watchFolder);
        QString folderName = folderInfo.baseName();
        QString clientFolderPath = "/" + folderName;

        // 如果客户端路径匹配文件夹名或其子路径
        if (normalizedClientPath == clientFolderPath) {
            return watchFolder;
        } else if (normalizedClientPath.startsWith(clientFolderPath + "/")) {
            QString relativePath = normalizedClientPath.mid(clientFolderPath.length() + 1);
            return watchFolder + "/" + relativePath;
        }
    }

    // 如果没有匹配的文件夹前缀，尝试在第一个文件夹中查找
    QString relativePath = normalizedClientPath;
    if (relativePath.startsWith("/")) relativePath = relativePath.mid(1);
    return m_watchFolders.first() + "/" + relativePath;
}

QString NetworkBackend::mapServerPathToClient(const QString& serverPath) {
    if (m_watchFolders.isEmpty()) return "/";

    // 遍历所有监听文件夹，找到匹配的服务器路径
    for (const QString& watchFolder : qAsConst(m_watchFolders)) {
        if (serverPath == watchFolder) {
            // 如果是根文件夹，映射为文件夹名
            QFileInfo folderInfo(watchFolder);
            return "/" + folderInfo.baseName();
        } else if (serverPath.startsWith(watchFolder + "/")) {
            // 如果是子路径，保留文件夹名前缀
            QFileInfo folderInfo(watchFolder);
            QString relativePath = serverPath.mid(watchFolder.length() + 1);
            return "/" + folderInfo.baseName() + "/" + relativePath;
        }
    }

    // 如果没有匹配的监听文件夹，返回根路径
    return "/";
}

// 重试机制实现
void NetworkBackend::handleRetryRequest(const RetryRequest& request) {
    if (request.retryCount >= m_maxRetries) {
        qWarning() << "Max retries reached for:" << request.imagePath;
        // 发送最终错误响应
        QByteArray errorResponse = createErrorResponse("Max retries exceeded", 503);
        if (request.socket && request.socket->state() == QTcpSocket::ConnectedState) {
            request.socket->write(errorResponse);
            request.socket->disconnectFromHost();
        }
        return;
    }

    // 添加到重试队列
    RetryRequest retryReq = request;
    retryReq.retryCount++;
    m_retryQueue.enqueue(retryReq);

    // 启动重试定时器
    if (!m_retryTimer->isActive()) {
        m_retryTimer->start();
    }
}

void NetworkBackend::processRetryQueue() {
    // 限制并发重试数量
    while (!m_retryQueue.isEmpty() && m_currentRetries < m_maxConcurrentRetries) {
        RetryRequest request = m_retryQueue.dequeue();

        // 检查socket是否仍然有效
        if (!request.socket || request.socket->state() != QTcpSocket::ConnectedState) {
            continue;
        }

        m_currentRetries++;

        // 在新线程中处理重试请求
        QTimer::singleShot(0, this, [this, request]() {
            QByteArray response;

            try {
                if (request.isThumbnail) {
                    response = handleThumbnail(request.imagePath);
                } else {
                    response = handleImageData(request.imagePath, request.headers);
                }

                // 成功处理
                if (request.socket && request.socket->state() == QTcpSocket::ConnectedState) {
                    request.socket->write(response);
                    request.socket->disconnectFromHost();
                }

            } catch (...) {
                // 重试失败，再次加入队列
                handleRetryRequest(request);
            }

            m_currentRetries--;

            // 继续处理队列
            if (!m_retryQueue.isEmpty()) {
                QTimer::singleShot(500, this, &NetworkBackend::processRetryQueue);
            }
        });
    }

    // 如果还有待处理的请求，稍后再试
    if (!m_retryQueue.isEmpty()) {
        m_retryTimer->start();
    }
}
