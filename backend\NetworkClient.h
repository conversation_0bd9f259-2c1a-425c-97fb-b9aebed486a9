#pragma once

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QTimer>
#include <QUrl>

class NetworkClient : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool connected READ isConnected NOTIFY connectedChanged)
    Q_PROPERTY(QString serverUrl READ serverUrl WRITE setServerUrl NOTIFY serverUrlChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY connectionStatusChanged)
    Q_PROPERTY(QJsonArray imageList READ imageList NOTIFY imageListChanged)

public:
    explicit NetworkClient(QObject *parent = nullptr);
    ~NetworkClient();

    // 属性访问器
    bool isConnected() const { return m_connected; }
    QString serverUrl() const { return m_serverUrl; }
    QString connectionStatus() const { return m_connectionStatus; }
    QJsonArray imageList() const { return m_imageList; }

    // 设置服务器URL
    void setServerUrl(const QString &url);

    // 可调用方法
    Q_INVOKABLE void connectToServer(const QString &host, int port);
    Q_INVOKABLE void disconnectFromServer();
    Q_INVOKABLE void loadImageList(const QString &path = "");
    Q_INVOKABLE QString getImageUrl(const QString &imagePath);
    Q_INVOKABLE QString getThumbnailUrl(const QString &imagePath);
    Q_INVOKABLE void testConnection();

signals:
    void connectedChanged();
    void serverUrlChanged();
    void connectionStatusChanged();
    void imageListChanged();
    void connectionError(const QString &error);
    void imageListLoaded(const QJsonArray &images);

private slots:
    void onTestConnectionFinished();
    void onImageListFinished();
    void onNetworkError(QNetworkReply::NetworkError error);

private:
    void setConnected(bool connected);
    void setConnectionStatus(const QString &status);
    void updateImageList(const QJsonArray &images);
    QString formatServerUrl(const QString &host, int port);

    QNetworkAccessManager *m_networkManager;
    QString m_serverUrl;
    QString m_connectionStatus;
    QJsonArray m_imageList;
    bool m_connected;
    
    // 当前请求跟踪
    QNetworkReply *m_currentTestReply;
    QNetworkReply *m_currentImageListReply;
};
