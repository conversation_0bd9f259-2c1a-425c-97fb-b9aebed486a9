import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qpa/qplatformmenu.h"
        name: "QPlatformMenu"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "MenuType"
            values: ["DefaultMenu", "EditMenu"]
        }
        Signal { name: "aboutToShow" }
        Signal { name: "aboutToHide" }
    }
    Component {
        file: "qpa/qplatformmenu.h"
        name: "QPlatformMenuItem"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "MenuRole"
            values: [
                "NoRole",
                "TextHeuristicRole",
                "ApplicationSpecificRole",
                "AboutQtRole",
                "AboutRole",
                "PreferencesRole",
                "QuitRole",
                "CutRole",
                "CopyRole",
                "PasteRole",
                "SelectAllRole",
                "RoleCount"
            ]
        }
        Signal { name: "activated" }
        Signal { name: "hovered" }
    }
    Component {
        file: "qpa/qplatformsystemtrayicon.h"
        name: "QPlatformSystemTrayIcon"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "ActivationReason"
            values: [
                "Unknown",
                "Context",
                "DoubleClick",
                "Trigger",
                "MiddleClick"
            ]
        }
        Enum {
            name: "MessageIcon"
            values: ["NoIcon", "Information", "Warning", "Critical"]
        }
        Signal {
            name: "activated"
            Parameter { name: "reason"; type: "QPlatformSystemTrayIcon::ActivationReason" }
        }
        Signal {
            name: "contextMenuRequested"
            Parameter { name: "globalPos"; type: "QPoint" }
            Parameter { name: "screen"; type: "QPlatformScreen"; isPointer: true; isTypeConstant: true }
        }
        Signal { name: "messageClicked" }
    }
    Component {
        file: "private/qquicklabsplatformcolordialog_p.h"
        name: "QQuickLabsPlatformColorDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickLabsPlatformDialog"
        extension: "QColorDialogOptions"
        extensionIsNamespace: true
        exports: ["Qt.labs.platform/ColorDialog 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "currentColor"
            type: "QColor"
            read: "currentColor"
            write: "setCurrentColor"
            notify: "currentColorChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "options"
            type: "QColorDialogOptions::ColorDialogOptions"
            read: "options"
            write: "setOptions"
            notify: "optionsChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "colorChanged" }
        Signal { name: "currentColorChanged" }
        Signal { name: "optionsChanged" }
    }
    Component {
        file: "private/qquicklabsplatformdialog_p.h"
        name: "QQuickLabsPlatformDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["Qt.labs.platform/Dialog 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
        Enum {
            name: "StandardCode"
            values: ["Rejected", "Accepted"]
        }
        Property {
            name: "data"
            type: "QObject"
            isList: true
            read: "data"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "parentWindow"
            type: "QWindow"
            isPointer: true
            read: "parentWindow"
            write: "setParentWindow"
            notify: "parentWindowChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "flags"
            type: "Qt::WindowFlags"
            read: "flags"
            write: "setFlags"
            notify: "flagsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "modality"
            type: "Qt::WindowModality"
            read: "modality"
            write: "setModality"
            notify: "modalityChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "result"
            type: "int"
            read: "result"
            write: "setResult"
            notify: "resultChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Signal { name: "parentWindowChanged" }
        Signal { name: "titleChanged" }
        Signal { name: "flagsChanged" }
        Signal { name: "modalityChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "resultChanged" }
        Method { name: "open" }
        Method { name: "close" }
        Method { name: "accept" }
        Method { name: "reject" }
        Method {
            name: "done"
            Parameter { name: "result"; type: "int" }
        }
    }
    Component {
        file: "private/qquicklabsplatformfiledialog_p.h"
        name: "QQuickLabsPlatformFileDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickLabsPlatformDialog"
        extension: "QFileDialogOptions"
        extensionIsNamespace: true
        exports: ["Qt.labs.platform/FileDialog 1.0"]
        exportMetaObjectRevisions: [256]
        Enum {
            name: "FileMode"
            values: ["OpenFile", "OpenFiles", "SaveFile"]
        }
        Property {
            name: "fileMode"
            type: "FileMode"
            read: "fileMode"
            write: "setFileMode"
            notify: "fileModeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "file"
            type: "QUrl"
            read: "file"
            write: "setFile"
            notify: "fileChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "files"
            type: "QUrl"
            isList: true
            read: "files"
            write: "setFiles"
            notify: "filesChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "currentFile"
            type: "QUrl"
            read: "currentFile"
            write: "setCurrentFile"
            notify: "currentFileChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "currentFiles"
            type: "QUrl"
            isList: true
            read: "currentFiles"
            write: "setCurrentFiles"
            notify: "currentFilesChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "folder"
            type: "QUrl"
            read: "folder"
            write: "setFolder"
            notify: "folderChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "options"
            type: "QFileDialogOptions::FileDialogOptions"
            read: "options"
            write: "setOptions"
            reset: "resetOptions"
            notify: "optionsChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "nameFilters"
            type: "QStringList"
            read: "nameFilters"
            write: "setNameFilters"
            reset: "resetNameFilters"
            notify: "nameFiltersChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "selectedNameFilter"
            type: "QQuickLabsPlatformFileNameFilter"
            isPointer: true
            read: "selectedNameFilter"
            index: 8
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "defaultSuffix"
            type: "QString"
            read: "defaultSuffix"
            write: "setDefaultSuffix"
            reset: "resetDefaultSuffix"
            notify: "defaultSuffixChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "acceptLabel"
            type: "QString"
            read: "acceptLabel"
            write: "setAcceptLabel"
            reset: "resetAcceptLabel"
            notify: "acceptLabelChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "rejectLabel"
            type: "QString"
            read: "rejectLabel"
            write: "setRejectLabel"
            reset: "resetRejectLabel"
            notify: "rejectLabelChanged"
            index: 11
            isFinal: true
        }
        Signal { name: "fileModeChanged" }
        Signal { name: "fileChanged" }
        Signal { name: "filesChanged" }
        Signal { name: "currentFileChanged" }
        Signal { name: "currentFilesChanged" }
        Signal { name: "folderChanged" }
        Signal { name: "optionsChanged" }
        Signal { name: "nameFiltersChanged" }
        Signal { name: "defaultSuffixChanged" }
        Signal { name: "acceptLabelChanged" }
        Signal { name: "rejectLabelChanged" }
    }
    Component {
        file: "private/qquicklabsplatformfiledialog_p.h"
        name: "QQuickLabsPlatformFileNameFilter"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            write: "setIndex"
            notify: "indexChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            notify: "nameChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "extensions"
            type: "QStringList"
            read: "extensions"
            notify: "extensionsChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "indexChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "extensionsChanged"
            Parameter { name: "extensions"; type: "QStringList" }
        }
    }
    Component {
        file: "private/qquicklabsplatformfolderdialog_p.h"
        name: "QQuickLabsPlatformFolderDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickLabsPlatformDialog"
        extension: "QFileDialogOptions"
        extensionIsNamespace: true
        exports: ["Qt.labs.platform/FolderDialog 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "folder"
            type: "QUrl"
            read: "folder"
            write: "setFolder"
            notify: "folderChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "currentFolder"
            type: "QUrl"
            read: "currentFolder"
            write: "setCurrentFolder"
            notify: "currentFolderChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "options"
            type: "QFileDialogOptions::FileDialogOptions"
            read: "options"
            write: "setOptions"
            reset: "resetOptions"
            notify: "optionsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "acceptLabel"
            type: "QString"
            read: "acceptLabel"
            write: "setAcceptLabel"
            reset: "resetAcceptLabel"
            notify: "acceptLabelChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "rejectLabel"
            type: "QString"
            read: "rejectLabel"
            write: "setRejectLabel"
            reset: "resetRejectLabel"
            notify: "rejectLabelChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "folderChanged" }
        Signal { name: "currentFolderChanged" }
        Signal { name: "optionsChanged" }
        Signal { name: "acceptLabelChanged" }
        Signal { name: "rejectLabelChanged" }
    }
    Component {
        file: "private/qquicklabsplatformfontdialog_p.h"
        name: "QQuickLabsPlatformFontDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickLabsPlatformDialog"
        extension: "QFontDialogOptions"
        extensionIsNamespace: true
        exports: ["Qt.labs.platform/FontDialog 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "currentFont"
            type: "QFont"
            read: "currentFont"
            write: "setCurrentFont"
            notify: "currentFontChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "options"
            type: "QFontDialogOptions::FontDialogOptions"
            read: "options"
            write: "setOptions"
            notify: "optionsChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "fontChanged" }
        Signal { name: "currentFontChanged" }
        Signal { name: "optionsChanged" }
    }
    Component {
        file: "private/qquicklabsplatformicon_p.h"
        name: "QQuickLabsPlatformIcon"
        accessSemantics: "value"
        Property { name: "source"; type: "QUrl"; read: "source"; write: "setSource"; index: 0; isFinal: true }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 1; isFinal: true }
        Property { name: "mask"; type: "bool"; read: "isMask"; write: "setMask"; index: 2; isFinal: true }
    }
    Component {
        file: "private/qquicklabsplatformmenu_p.h"
        name: "QQuickLabsPlatformMenu"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QObject"
        extension: "QPlatformMenu"
        extensionIsNamespace: true
        interfaces: ["QQmlParserStatus"]
        exports: ["Qt.labs.platform/Menu 1.0", "Qt.labs.platform/Menu 1.1"]
        exportMetaObjectRevisions: [256, 257]
        Property {
            name: "data"
            type: "QObject"
            isList: true
            read: "data"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "items"
            type: "QQuickLabsPlatformMenuItem"
            isList: true
            read: "items"
            notify: "itemsChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "menuBar"
            type: "QQuickLabsPlatformMenuBar"
            isPointer: true
            read: "menuBar"
            notify: "menuBarChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "parentMenu"
            type: "QQuickLabsPlatformMenu"
            isPointer: true
            read: "parentMenu"
            notify: "parentMenuChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "systemTrayIcon"
            type: "QQuickLabsPlatformSystemTrayIcon"
            isPointer: true
            read: "systemTrayIcon"
            notify: "systemTrayIconChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "menuItem"
            type: "QQuickLabsPlatformMenuItem"
            isPointer: true
            read: "menuItem"
            index: 5
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "minimumWidth"
            type: "int"
            read: "minimumWidth"
            write: "setMinimumWidth"
            notify: "minimumWidthChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "type"
            type: "QPlatformMenu::MenuType"
            read: "type"
            write: "setType"
            notify: "typeChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "icon"
            revision: 257
            type: "QQuickLabsPlatformIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 12
            isFinal: true
        }
        Signal { name: "aboutToShow" }
        Signal { name: "aboutToHide" }
        Signal { name: "itemsChanged" }
        Signal { name: "menuBarChanged" }
        Signal { name: "parentMenuChanged" }
        Signal { name: "systemTrayIconChanged" }
        Signal { name: "titleChanged" }
        Signal { name: "enabledChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "minimumWidthChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "typeChanged" }
        Signal { name: "iconChanged"; revision: 257 }
        Method { name: "open"; isJavaScriptFunction: true }
        Method { name: "close" }
        Method { name: "updateIcon" }
        Method {
            name: "addItem"
            Parameter { name: "item"; type: "QQuickLabsPlatformMenuItem"; isPointer: true }
        }
        Method {
            name: "insertItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickLabsPlatformMenuItem"; isPointer: true }
        }
        Method {
            name: "removeItem"
            Parameter { name: "item"; type: "QQuickLabsPlatformMenuItem"; isPointer: true }
        }
        Method {
            name: "addMenu"
            Parameter { name: "menu"; type: "QQuickLabsPlatformMenu"; isPointer: true }
        }
        Method {
            name: "insertMenu"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "menu"; type: "QQuickLabsPlatformMenu"; isPointer: true }
        }
        Method {
            name: "removeMenu"
            Parameter { name: "menu"; type: "QQuickLabsPlatformMenu"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/qquicklabsplatformmenubar_p.h"
        name: "QQuickLabsPlatformMenuBar"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["Qt.labs.platform/MenuBar 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "data"
            type: "QObject"
            isList: true
            read: "data"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "menus"
            type: "QQuickLabsPlatformMenu"
            isList: true
            read: "menus"
            notify: "menusChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "window"
            type: "QWindow"
            isPointer: true
            read: "window"
            write: "setWindow"
            notify: "windowChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "menusChanged" }
        Signal { name: "windowChanged" }
        Method {
            name: "addMenu"
            Parameter { name: "menu"; type: "QQuickLabsPlatformMenu"; isPointer: true }
        }
        Method {
            name: "insertMenu"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "menu"; type: "QQuickLabsPlatformMenu"; isPointer: true }
        }
        Method {
            name: "removeMenu"
            Parameter { name: "menu"; type: "QQuickLabsPlatformMenu"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/qquicklabsplatformmenuitem_p.h"
        name: "QQuickLabsPlatformMenuItem"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QPlatformMenuItem"
        extensionIsNamespace: true
        interfaces: ["QQmlParserStatus"]
        exports: [
            "Qt.labs.platform/MenuItem 1.0",
            "Qt.labs.platform/MenuItem 1.1"
        ]
        exportMetaObjectRevisions: [256, 257]
        Property {
            name: "menu"
            type: "QQuickLabsPlatformMenu"
            isPointer: true
            read: "menu"
            notify: "menuChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "subMenu"
            type: "QQuickLabsPlatformMenu"
            isPointer: true
            read: "subMenu"
            notify: "subMenuChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "group"
            type: "QQuickLabsPlatformMenuItemGroup"
            isPointer: true
            read: "group"
            write: "setGroup"
            notify: "groupChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "separator"
            type: "bool"
            read: "isSeparator"
            write: "setSeparator"
            notify: "separatorChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "checkable"
            type: "bool"
            read: "isCheckable"
            write: "setCheckable"
            notify: "checkableChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "checked"
            type: "bool"
            read: "isChecked"
            write: "setChecked"
            notify: "checkedChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "role"
            type: "QPlatformMenuItem::MenuRole"
            read: "role"
            write: "setRole"
            notify: "roleChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "shortcut"
            type: "QVariant"
            read: "shortcut"
            write: "setShortcut"
            notify: "shortcutChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "icon"
            revision: 257
            type: "QQuickLabsPlatformIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 12
            isFinal: true
        }
        Signal { name: "triggered" }
        Signal { name: "hovered" }
        Signal { name: "menuChanged" }
        Signal { name: "subMenuChanged" }
        Signal { name: "groupChanged" }
        Signal { name: "enabledChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "separatorChanged" }
        Signal { name: "checkableChanged" }
        Signal { name: "checkedChanged" }
        Signal { name: "roleChanged" }
        Signal { name: "textChanged" }
        Signal { name: "shortcutChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "iconChanged"; revision: 257 }
        Method { name: "toggle" }
        Method { name: "activate" }
        Method { name: "updateIcon" }
    }
    Component {
        file: "private/qquicklabsplatformmenuitemgroup_p.h"
        name: "QQuickLabsPlatformMenuItemGroup"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["Qt.labs.platform/MenuItemGroup 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "exclusive"
            type: "bool"
            read: "isExclusive"
            write: "setExclusive"
            notify: "exclusiveChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "checkedItem"
            type: "QQuickLabsPlatformMenuItem"
            isPointer: true
            read: "checkedItem"
            write: "setCheckedItem"
            notify: "checkedItemChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "items"
            type: "QQuickLabsPlatformMenuItem"
            isList: true
            read: "items"
            notify: "itemsChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "triggered"
            Parameter { name: "item"; type: "QQuickLabsPlatformMenuItem"; isPointer: true }
        }
        Signal {
            name: "hovered"
            Parameter { name: "item"; type: "QQuickLabsPlatformMenuItem"; isPointer: true }
        }
        Signal { name: "enabledChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "exclusiveChanged" }
        Signal { name: "checkedItemChanged" }
        Signal { name: "itemsChanged" }
        Method {
            name: "addItem"
            Parameter { name: "item"; type: "QQuickLabsPlatformMenuItem"; isPointer: true }
        }
        Method {
            name: "removeItem"
            Parameter { name: "item"; type: "QQuickLabsPlatformMenuItem"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/qquicklabsplatformmenuseparator_p.h"
        name: "QQuickLabsPlatformMenuSeparator"
        accessSemantics: "reference"
        prototype: "QQuickLabsPlatformMenuItem"
        exports: [
            "Qt.labs.platform/MenuSeparator 1.0",
            "Qt.labs.platform/MenuSeparator 1.1"
        ]
        exportMetaObjectRevisions: [256, 257]
    }
    Component {
        file: "private/qquicklabsplatformmessagedialog_p.h"
        name: "QQuickLabsPlatformMessageDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickLabsPlatformDialog"
        extension: "QPlatformDialogHelper"
        extensionIsNamespace: true
        exports: ["Qt.labs.platform/MessageDialog 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "informativeText"
            type: "QString"
            read: "informativeText"
            write: "setInformativeText"
            notify: "informativeTextChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "detailedText"
            type: "QString"
            read: "detailedText"
            write: "setDetailedText"
            notify: "detailedTextChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "buttons"
            type: "QPlatformDialogHelper::StandardButtons"
            read: "buttons"
            write: "setButtons"
            notify: "buttonsChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "textChanged" }
        Signal { name: "informativeTextChanged" }
        Signal { name: "detailedTextChanged" }
        Signal { name: "buttonsChanged" }
        Signal {
            name: "clicked"
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
        }
        Signal { name: "okClicked" }
        Signal { name: "saveClicked" }
        Signal { name: "saveAllClicked" }
        Signal { name: "openClicked" }
        Signal { name: "yesClicked" }
        Signal { name: "yesToAllClicked" }
        Signal { name: "noClicked" }
        Signal { name: "noToAllClicked" }
        Signal { name: "abortClicked" }
        Signal { name: "retryClicked" }
        Signal { name: "ignoreClicked" }
        Signal { name: "closeClicked" }
        Signal { name: "cancelClicked" }
        Signal { name: "discardClicked" }
        Signal { name: "helpClicked" }
        Signal { name: "applyClicked" }
        Signal { name: "resetClicked" }
        Signal { name: "restoreDefaultsClicked" }
        Method {
            name: "handleClick"
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
        }
    }
    Component {
        file: "private/qquicklabsplatformstandardpaths_p.h"
        name: "QQuickLabsPlatformStandardPaths"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QStandardPaths"
        extensionIsNamespace: true
        exports: ["Qt.labs.platform/StandardPaths 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256]
        Method {
            name: "displayName"
            type: "QString"
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
        }
        Method {
            name: "findExecutable"
            type: "QUrl"
            Parameter { name: "executableName"; type: "QString" }
            Parameter { name: "paths"; type: "QStringList" }
        }
        Method {
            name: "findExecutable"
            type: "QUrl"
            isCloned: true
            Parameter { name: "executableName"; type: "QString" }
        }
        Method {
            name: "locate"
            type: "QUrl"
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
            Parameter { name: "options"; type: "QStandardPaths::LocateOptions" }
        }
        Method {
            name: "locate"
            type: "QUrl"
            isCloned: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
        }
        Method {
            name: "locateAll"
            type: "QUrl"
            isList: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
            Parameter { name: "options"; type: "QStandardPaths::LocateOptions" }
        }
        Method {
            name: "locateAll"
            type: "QUrl"
            isList: true
            isCloned: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
        }
        Method {
            name: "setTestModeEnabled"
            Parameter { name: "testMode"; type: "bool" }
        }
        Method {
            name: "standardLocations"
            type: "QUrl"
            isList: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
        }
        Method {
            name: "writableLocation"
            type: "QUrl"
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
        }
    }
    Component {
        file: "private/qquicklabsplatformsystemtrayicon_p.h"
        name: "QQuickLabsPlatformSystemTrayIcon"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QPlatformSystemTrayIcon"
        extensionIsNamespace: true
        interfaces: ["QQmlParserStatus"]
        exports: [
            "Qt.labs.platform/SystemTrayIcon 1.0",
            "Qt.labs.platform/SystemTrayIcon 1.1"
        ]
        exportMetaObjectRevisions: [256, 257]
        Property {
            name: "available"
            type: "bool"
            read: "isAvailable"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "supportsMessages"
            type: "bool"
            read: "supportsMessages"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "tooltip"
            type: "QString"
            read: "tooltip"
            write: "setTooltip"
            notify: "tooltipChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "menu"
            type: "QQuickLabsPlatformMenu"
            isPointer: true
            read: "menu"
            write: "setMenu"
            notify: "menuChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "geometry"
            revision: 257
            type: "QRect"
            read: "geometry"
            notify: "geometryChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "icon"
            revision: 257
            type: "QQuickLabsPlatformIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 6
            isFinal: true
        }
        Signal {
            name: "activated"
            Parameter { name: "reason"; type: "QPlatformSystemTrayIcon::ActivationReason" }
        }
        Signal { name: "messageClicked" }
        Signal { name: "visibleChanged" }
        Signal { name: "tooltipChanged" }
        Signal { name: "menuChanged" }
        Signal { name: "geometryChanged"; revision: 257 }
        Signal { name: "iconChanged"; revision: 257 }
        Method { name: "show" }
        Method { name: "hide" }
        Method {
            name: "showMessage"
            Parameter { name: "title"; type: "QString" }
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "iconType"; type: "QPlatformSystemTrayIcon::MessageIcon" }
            Parameter { name: "msecs"; type: "int" }
        }
        Method {
            name: "showMessage"
            isCloned: true
            Parameter { name: "title"; type: "QString" }
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "iconType"; type: "QPlatformSystemTrayIcon::MessageIcon" }
        }
        Method {
            name: "showMessage"
            isCloned: true
            Parameter { name: "title"; type: "QString" }
            Parameter { name: "message"; type: "QString" }
        }
        Method { name: "updateIcon" }
    }
    Component {
        file: "qstandardpaths.h"
        name: "QStandardPaths"
        accessSemantics: "value"
        Enum {
            name: "StandardLocation"
            values: [
                "DesktopLocation",
                "DocumentsLocation",
                "FontsLocation",
                "ApplicationsLocation",
                "MusicLocation",
                "MoviesLocation",
                "PicturesLocation",
                "TempLocation",
                "HomeLocation",
                "AppLocalDataLocation",
                "CacheLocation",
                "GenericDataLocation",
                "RuntimeLocation",
                "ConfigLocation",
                "DownloadLocation",
                "GenericCacheLocation",
                "GenericConfigLocation",
                "AppDataLocation",
                "AppConfigLocation",
                "PublicShareLocation",
                "TemplatesLocation",
                "StateLocation",
                "GenericStateLocation"
            ]
        }
        Enum {
            name: "LocateOptions"
            alias: "LocateOption"
            isFlag: true
            values: ["LocateFile", "LocateDirectory"]
        }
    }
}
