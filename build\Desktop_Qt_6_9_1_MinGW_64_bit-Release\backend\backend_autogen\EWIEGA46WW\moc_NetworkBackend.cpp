/****************************************************************************
** Meta object code from reading C++ file 'NetworkBackend.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../backend/NetworkBackend.h"
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'NetworkBackend.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14NetworkBackendE_t {};
} // unnamed namespace

template <> constexpr inline auto NetworkBackend::qt_create_metaobjectdata<qt_meta_tag_ZN14NetworkBackendE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "NetworkBackend",
        "localAddressesChanged",
        "",
        "serverStatusChanged",
        "requestStatsChanged",
        "requestReceived",
        "clientIP",
        "path",
        "onNewConnection",
        "onSocketReadyRead",
        "onSocketDisconnected",
        "refreshLocalAddresses",
        "startImageServer",
        "port",
        "stopImageServer",
        "isServerRunning",
        "serverPort",
        "serverUrl",
        "activeRequests",
        "addWatchFolder",
        "folderPath",
        "removeWatchFolder",
        "getWatchFolders",
        "ipv4Addresses",
        "ipv6Addresses",
        "serverRunning"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'localAddressesChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'serverStatusChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'requestStatsChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'requestReceived'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 7 },
        }}),
        // Slot 'onNewConnection'
        QtMocHelpers::SlotData<void()>(8, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onSocketReadyRead'
        QtMocHelpers::SlotData<void()>(9, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onSocketDisconnected'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPrivate, QMetaType::Void),
        // Method 'refreshLocalAddresses'
        QtMocHelpers::MethodData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'startImageServer'
        QtMocHelpers::MethodData<bool(int)>(12, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::Int, 13 },
        }}),
        // Method 'startImageServer'
        QtMocHelpers::MethodData<bool()>(12, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Bool),
        // Method 'stopImageServer'
        QtMocHelpers::MethodData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'isServerRunning'
        QtMocHelpers::MethodData<bool() const>(15, 2, QMC::AccessPublic, QMetaType::Bool),
        // Method 'serverPort'
        QtMocHelpers::MethodData<int() const>(16, 2, QMC::AccessPublic, QMetaType::Int),
        // Method 'serverUrl'
        QtMocHelpers::MethodData<QString() const>(17, 2, QMC::AccessPublic, QMetaType::QString),
        // Method 'activeRequests'
        QtMocHelpers::MethodData<int() const>(18, 2, QMC::AccessPublic, QMetaType::Int),
        // Method 'addWatchFolder'
        QtMocHelpers::MethodData<void(const QString &)>(19, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 20 },
        }}),
        // Method 'removeWatchFolder'
        QtMocHelpers::MethodData<void(const QString &)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 20 },
        }}),
        // Method 'getWatchFolders'
        QtMocHelpers::MethodData<QStringList() const>(22, 2, QMC::AccessPublic, QMetaType::QStringList),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'ipv4Addresses'
        QtMocHelpers::PropertyData<QStringList>(23, QMetaType::QStringList, QMC::DefaultPropertyFlags, 0),
        // property 'ipv6Addresses'
        QtMocHelpers::PropertyData<QStringList>(24, QMetaType::QStringList, QMC::DefaultPropertyFlags, 0),
        // property 'serverRunning'
        QtMocHelpers::PropertyData<bool>(25, QMetaType::Bool, QMC::DefaultPropertyFlags, 1),
        // property 'serverPort'
        QtMocHelpers::PropertyData<int>(16, QMetaType::Int, QMC::DefaultPropertyFlags, 1),
        // property 'serverUrl'
        QtMocHelpers::PropertyData<QString>(17, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'activeRequests'
        QtMocHelpers::PropertyData<int>(18, QMetaType::Int, QMC::DefaultPropertyFlags, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<NetworkBackend, qt_meta_tag_ZN14NetworkBackendE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject NetworkBackend::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14NetworkBackendE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14NetworkBackendE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14NetworkBackendE_t>.metaTypes,
    nullptr
} };

void NetworkBackend::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<NetworkBackend *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->localAddressesChanged(); break;
        case 1: _t->serverStatusChanged(); break;
        case 2: _t->requestStatsChanged(); break;
        case 3: _t->requestReceived((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 4: _t->onNewConnection(); break;
        case 5: _t->onSocketReadyRead(); break;
        case 6: _t->onSocketDisconnected(); break;
        case 7: _t->refreshLocalAddresses(); break;
        case 8: { bool _r = _t->startImageServer((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 9: { bool _r = _t->startImageServer();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 10: _t->stopImageServer(); break;
        case 11: { bool _r = _t->isServerRunning();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 12: { int _r = _t->serverPort();
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 13: { QString _r = _t->serverUrl();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 14: { int _r = _t->activeRequests();
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 15: _t->addWatchFolder((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 16: _t->removeWatchFolder((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 17: { QStringList _r = _t->getWatchFolders();
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (NetworkBackend::*)()>(_a, &NetworkBackend::localAddressesChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkBackend::*)()>(_a, &NetworkBackend::serverStatusChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkBackend::*)()>(_a, &NetworkBackend::requestStatsChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (NetworkBackend::*)(const QString & , const QString & )>(_a, &NetworkBackend::requestReceived, 3))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QStringList*>(_v) = _t->ipv4Addresses(); break;
        case 1: *reinterpret_cast<QStringList*>(_v) = _t->ipv6Addresses(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->isServerRunning(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->serverPort(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->serverUrl(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->activeRequests(); break;
        default: break;
        }
    }
}

const QMetaObject *NetworkBackend::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *NetworkBackend::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14NetworkBackendE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int NetworkBackend::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 18;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void NetworkBackend::localAddressesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void NetworkBackend::serverStatusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void NetworkBackend::requestStatsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void NetworkBackend::requestReceived(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}
QT_WARNING_POP
