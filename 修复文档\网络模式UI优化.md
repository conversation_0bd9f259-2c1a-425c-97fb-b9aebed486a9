# 网络模式UI优化文档

## 🎯 **优化目标**

1. **ImagePage的网络模式URL构造**
2. **缩略图的网络请求逻辑**  
3. **错误处理和重试机制**
4. **用户主导的重试交互**

## 🔧 **主要优化内容**

### 1. **网络模式检测与URL构造**

#### **1.1 添加网络模式属性**
```qml
// imagePage.qml
property bool isNetworkMode: typeof networkBackend !== 'undefined' && networkBackend.serverRunning
property string networkBaseUrl: isNetworkMode ? networkBackend.serverUrl : ""
```

#### **1.2 网络URL构造辅助函数**
```qml
function getNetworkImageUrl(filePath) {
    if (!isNetworkMode || !filePath) return "";
    return networkBaseUrl + "/api/image" + filePath;
}

function getNetworkThumbnailUrl(filePath) {
    if (!isNetworkMode || !filePath) return "";
    return networkBaseUrl + "/api/thumbnail" + filePath;
}
```

### 2. **智能缩略图加载逻辑**

#### **2.1 统一加载函数**
```qml
function loadThumbnail() {
    if (isNetworkMode) {
        // 网络模式：构造网络URL
        currentSource = getNetworkThumbnailUrl(model.filePath);
    } else {
        // 本地模式：使用ImageProcessor
        var thumbUrl = imageProcessor.getThumbnailImage(index);
        if (thumbUrl && thumbUrl.length > 0) {
            currentSource = thumbUrl;
        }
    }
}
```

#### **2.2 动态模式切换**
```qml
Connections {
    target: typeof networkBackend !== 'undefined' ? networkBackend : null
    function onServerStatusChanged() {
        if (!model.isFolder) {
            Qt.callLater(function() {
                imageCell.loadThumbnail();
            });
        }
    }
}
```

### 3. **改进的错误处理机制**

#### **3.1 重试状态管理**
```qml
Rectangle {
    id: imageCell
    property string currentSource: ""
    property bool isRetrying: false
    property int retryCount: 0
    property int maxRetries: 3
}
```

#### **3.2 智能错误检测**
```qml
onStatusChanged: {
    if (status === Image.Error) {
        errorPlaceholder.visible = true;
        retryButton.visible = true;
    } else if (status === Image.Ready) {
        errorPlaceholder.visible = false;
        retryButton.visible = false;
        everReady = true;
        imageCell.retryCount = 0; // 重置重试计数
    } else if (status === Image.Loading) {
        retryButton.visible = false;
    }
}
```

### 4. **用户主导的重试按钮**

#### **4.1 透明重试按钮设计**
```qml
Rectangle {
    id: retryButton
    anchors.centerIn: parent
    width: 40; height: 40
    color: Qt.rgba(0, 0, 0, 0.6)
    radius: 20
    visible: false
    
    Text {
        anchors.centerIn: parent
        text: "🔄"  // 双旋转箭头图标
        font.pixelSize: 20
        color: "white"
    }
}
```

#### **4.2 用户主导的重试逻辑**
```qml
MouseArea {
    anchors.fill: parent
    hoverEnabled: true
    onClicked: {
        retryButton.visible = false;  // 立即隐藏按钮
        imageCell.retryLoad();        // 执行重试
    }
    onEntered: parent.color = Qt.rgba(0, 0, 0, 0.8)
    onExited: parent.color = Qt.rgba(0, 0, 0, 0.6)
}
```

#### **4.3 防止线程错乱的重试函数**
```qml
function retryLoad() {
    if (retryCount < maxRetries && !isRetrying) {
        isRetrying = true;
        retryCount++;
        Qt.callLater(function() {
            loadThumbnail();
            isRetrying = false;
        });
    }
}
```

### 5. **ImagePreviewWindow网络支持**

#### **5.1 预览窗口网络模式**
```qml
function loadImageByIndex(index) {
    let imagePath = imageProcessor.getFilePath(index)
    if (imagePath) {
        let isNetworkMode = typeof networkBackend !== 'undefined' && networkBackend.serverRunning;
        
        if (isNetworkMode) {
            // 网络模式：使用网络URL
            let networkUrl = networkBackend.serverUrl + "/api/image" + imagePath;
            previewImage.source = networkUrl;
        } else {
            // 本地模式：使用本地文件路径
            // ... 原有逻辑
        }
    }
}
```

### 6. **加载指示器优化**

#### **6.1 智能加载状态**
```qml
BusyIndicator {
    anchors.centerIn: parent
    running: (isNetworkMode ? (thumbnail.status === Image.Loading) : imageProcessor.isItemLoading(index)) && !thumbnail.everReady
    visible: running
}
```

## 🎨 **用户体验特性**

### ✅ **重试按钮特性**
- **透明设计**: 半透明黑色背景，不遮挡内容
- **双旋转箭头**: 🔄 直观的重试图标
- **悬停效果**: 鼠标悬停时背景变深
- **用户主导**: 只有用户点击才执行重试
- **防止错乱**: 重试期间禁用按钮，防止重复请求

### ✅ **智能模式切换**
- **自动检测**: 根据networkBackend状态自动切换模式
- **无缝切换**: 服务器启停时自动重新加载缩略图
- **状态保持**: 切换模式时保持当前浏览状态

### ✅ **错误处理策略**
- **最大重试次数**: 防止无限重试
- **重试计数重置**: 成功加载后重置计数
- **状态同步**: 加载状态与UI状态同步

## 🔄 **工作流程**

1. **初始化**: 检测网络模式，选择合适的加载策略
2. **加载缩略图**: 根据模式构造URL或使用本地缓存
3. **错误处理**: 加载失败时显示重试按钮
4. **用户重试**: 用户点击重试按钮触发重新加载
5. **状态管理**: 跟踪重试次数，防止无限重试
6. **模式切换**: 网络状态变化时自动适应

## 📊 **性能优化**

- **延迟加载**: 使用Qt.callLater避免阻塞UI
- **状态缓存**: 避免重复的模式检测
- **智能重试**: 限制重试次数和频率
- **资源管理**: 及时清理失败的请求

这些优化确保了网络模式下的图片浏览体验与本地模式一样流畅，同时提供了用户友好的错误处理机制。
