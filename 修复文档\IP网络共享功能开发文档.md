# IP网络共享功能开发文档（基于现有架构优化版）

## 0. 进度标注
> 例：已完成 1-1, 1-2, 2-1-1, 3-2

---

## 1. 基于现有架构的精简设计

### 1-1. 复用现有NetworkBackend
- **扩展策略**：在现有NetworkBackend基础上添加HTTP服务功能
- **保持精简**：避免创建新的网络引擎类，直接扩展现有功能
- **IP地址复用**：利用现有的IPv4/IPv6地址获取逻辑

### 1-2. 扩展现有ServerMode
- **HTTP服务集成**：在现有ServerMode中添加QHttpServer支持
- **连接管理复用**：利用现有的ConnectionID和会话管理
- **单例保证**：基于现有架构确保服务唯一性

### 1-3. 集成ImageProcessor
- **直接扩展**：在ImageProcessor中添加网络服务相关方法
- **缓存复用**：利用现有的缩略图缓存机制
- **模型集成**：复用QAbstractListModel架构

---

## 2. 精简网络通信层（基于现有架构）

### 2-1. 扩展NetworkBackend类
```cpp
// 在现有NetworkBackend.h中添加HTTP服务功能
class NetworkBackend : public QObject {
    Q_OBJECT
    Q_PROPERTY(QStringList ipv4Addresses READ ipv4Addresses NOTIFY localAddressesChanged)
    Q_PROPERTY(QStringList ipv6Addresses READ ipv6Addresses NOTIFY localAddressesChanged)
    // 新增网络服务属性
    Q_PROPERTY(bool serverRunning READ isServerRunning NOTIFY serverStatusChanged)
    Q_PROPERTY(int serverPort READ serverPort NOTIFY serverStatusChanged)
    Q_PROPERTY(QString serverUrl READ serverUrl NOTIFY serverStatusChanged)

public:
    explicit NetworkBackend(QObject* parent = nullptr);

    // 现有功能
    QStringList ipv4Addresses() const { return m_ipv4Addresses; }
    QStringList ipv6Addresses() const { return m_ipv6Addresses; }
    Q_INVOKABLE void refreshLocalAddresses();

    // 新增HTTP服务功能
    Q_INVOKABLE bool startImageServer(int port = 3333);
    Q_INVOKABLE void stopImageServer();
    Q_INVOKABLE bool isServerRunning() const { return m_httpServer != nullptr; }
    Q_INVOKABLE int serverPort() const { return m_currentPort; }
    Q_INVOKABLE QString serverUrl() const;

signals:
    void localAddressesChanged();
    void serverStatusChanged();
    void requestReceived(const QString &clientIP, const QString &path);

private:
    // 现有成员
    QStringList m_ipv4Addresses;
    QStringList m_ipv6Addresses;

    // 新增HTTP服务成员
    QHttpServer* m_httpServer = nullptr;
    int m_currentPort = 0;
    void setupImageRoutes();
    QHttpServerResponse handleImageList(const QHttpServerRequest &request);
    QHttpServerResponse handleImageData(const QHttpServerRequest &request);
    QHttpServerResponse handleThumbnail(const QHttpServerRequest &request);
};
```

### 2-2. 精简API端点设计
- **GET /api/images?path={folder}** - 获取指定文件夹图片列表
- **GET /api/image/{path}** - 获取完整图片数据（支持Range请求）
- **GET /api/thumbnail/{path}** - 获取缩略图数据

---

## 3. 扩展ImageProcessor（复用现有缓存）

### 3-1. 在现有ImageProcessor中添加网络方法
```cpp
// 在现有ImageProcessor类中添加网络服务方法
class ImageProcessor : public QAbstractListModel {
    // 现有代码保持不变...

    // 新增网络服务方法
    Q_INVOKABLE QJsonObject getNetworkImageList(const QString &folderPath);
    Q_INVOKABLE QByteArray getNetworkThumbnail(int index);
    Q_INVOKABLE QByteArray getNetworkFullImage(int index);
    Q_INVOKABLE QString getImagePathByIndex(int index);

    // 网络并发控制（简化版）
    Q_INVOKABLE bool canServeRequest(qint64 estimatedSize);

signals:
    void networkRequestServed(const QString &path, qint64 size);

private:
    // 复用现有缓存机制
    // m_thumbnailCache - 已有的缩略图缓存
    // thumbPathForFile() - 已有的缩略图路径生成

    // 简化的并发控制
    QAtomicInt m_activeNetworkRequests{0};
    static constexpr int MAX_CONCURRENT_REQUESTS = 5;
    static constexpr qint64 MAX_BANDWIDTH_PER_SEC = 12500000; // 12.5MB/s
};
```

### 3-2. 复用现有缓存机制
- **缩略图缓存**：直接使用现有m_thumbnailCache
- **缓存路径**：复用现有thumbPathForFile()方法
- **文件列表**：复用现有m_fileInfoList和模型数据

### 3-3. 传输优化策略
- **源文件直传**：不压缩，保持原始质量
- **Range请求**：支持分块传输和断点续传
- **并发限制**：简单的原子计数器控制并发数

---

## 4. 并发与性能控制

### 4-1. 请求队列管理
```cpp
class RequestQueue {
public:
    // 4-1-1. 请求分类
    enum RequestType { Thumbnail, FullImage, Metadata };
    
    // 4-1-2. 队列控制
    void enqueueRequest(const NetworkRequest &request);
    NetworkRequest dequeueRequest();
    
    // 4-1-3. 优先级管理
    void setPriority(RequestType type, int priority);
    
private:
    QQueue<NetworkRequest> m_thumbnailQueue;
    QQueue<NetworkRequest> m_fullImageQueue;
    QMutex m_queueMutex;
};
```

### 4-2. 带宽控制算法
- **令牌桶算法**：平滑流量控制
- **动态调整**：根据网络状况自适应
- **优先级队列**：缩略图优先，大图排队

### 4-3. 资源监控
- **内存使用**：限制缓存大小，及时释放
- **磁盘I/O**：监控读取速度，避免过载
- **网络带宽**：实时监控传输速率

---

## 5. 客户端集成

### 5-1. QML界面扩展
```qml
// 5-1-1. 网络设置页面
NetworkSettingsPage {
    property bool serverEnabled: false
    property int serverPort: 3333
    property string serverAddress: "0.0.0.0"
    property string remoteCachePath: ""
    
    // 服务器控制
    function startNetworkServer() { imageNetworkEngine.startServer(serverPort) }
    function stopNetworkServer() { imageNetworkEngine.stopServer() }
    
    // 状态显示
    Text { text: "服务状态: " + (serverEnabled ? "运行中" : "已停止") }
    Text { text: "访问地址: http://" + getLocalIP() + ":" + serverPort }
}
```

### 5-2. 远程图片浏览与本地缓存
```qml
// 5-2-1. 网络图片页面（带本地缓存）
RemoteImagePage {
    property string remoteServerUrl: ""
    property string localCachePath: ""

    // 本地缓存管理
    RemoteCacheManager {
        id: cacheManager
        cachePath: localCachePath
        maxCacheSize: 1024 * 1024 * 1024 // 1GB缓存限制

        // 缓存策略：缩略图永久缓存，原图按LRU策略清理
        onCacheUpdated: updateImageModel()
    }

    // 复用现有imagePage.qml组件结构
    GridLayout {
        Repeater {
            model: remoteImageModel
            delegate: RemoteImageCell {
                thumbnailUrl: cacheManager.getCachedThumbnail(model.path) || model.thumbnailUrl
                onClicked: openRemotePreview(model.path)
            }
        }
    }
}
```

---

## 6. 安全与配置

### 6-1. 基础安全措施
- **路径验证**：防止目录遍历攻击
- **文件类型检查**：仅允许图片文件访问
- **访问频率限制**：每个IP每10秒最多60次请求（正常用户点击操作不会达到此限制）
- **连接中断处理**：服务停止时发送中断包并释放所有资源

### 6-2. 配置管理
```cpp
// 6-2-1. 网络配置
struct NetworkConfig {
    int serverPort = 3333;
    QString bindAddress = "0.0.0.0";
    int maxConcurrentConnections = 10;
    qint64 maxBandwidthBps = 12500000;
    QStringList allowedPaths;
    bool enableCORS = true;
    QString remoteCachePath = "cache/remote";
    qint64 maxCacheSizeBytes = 1073741824; // 1GB
    int requestsPerTenSeconds = 60; // 每10秒最多60次请求
};
```

---

## 7. 远程缓存管理系统

### 7-1. 本地缓存架构
```cpp
// 7-1-1. 远程缓存管理器
class RemoteCacheManager : public QObject {
    Q_OBJECT
    Q_PROPERTY(QString cachePath READ cachePath WRITE setCachePath NOTIFY cachePathChanged)
    Q_PROPERTY(qint64 cacheSize READ cacheSize NOTIFY cacheSizeChanged)
    Q_PROPERTY(qint64 maxCacheSize READ maxCacheSize WRITE setMaxCacheSize)

public:
    explicit RemoteCacheManager(QObject *parent = nullptr);

    // 7-1-2. 缓存路径管理
    Q_INVOKABLE void setCachePath(const QString &newPath);
    Q_INVOKABLE QString cachePath() const { return m_cachePath; }
    Q_INVOKABLE void clearCache();
    Q_INVOKABLE void migrateCacheTo(const QString &newPath);

    // 7-1-3. 缓存操作
    Q_INVOKABLE QString getCachedThumbnail(const QString &remotePath);
    Q_INVOKABLE QString getCachedFullImage(const QString &remotePath);
    Q_INVOKABLE bool isCached(const QString &remotePath, bool thumbnail = true);
    Q_INVOKABLE void cacheImage(const QString &remotePath, const QByteArray &data, bool thumbnail = true);

    // 7-1-4. 缓存统计
    Q_INVOKABLE qint64 cacheSize() const { return m_currentCacheSize; }
    Q_INVOKABLE qint64 maxCacheSize() const { return m_maxCacheSize; }
    Q_INVOKABLE int cachedItemCount() const;
    Q_INVOKABLE QString formatCacheSize() const;

signals:
    void cachePathChanged();
    void cacheSizeChanged();
    void cacheCleared();
    void cacheMigrated(const QString &oldPath, const QString &newPath);

private:
    void updateCacheSize();
    void cleanupOldCache();
    QString generateCacheKey(const QString &remotePath, bool thumbnail);
    void ensureCacheDirectory();

    QString m_cachePath;
    qint64 m_currentCacheSize;
    qint64 m_maxCacheSize;
    QHash<QString, QDateTime> m_accessTimes; // LRU管理
    QMutex m_cacheMutex;
};
```

### 7-2. 缓存策略实现
```cpp
// 7-2-1. 智能缓存策略
void RemoteCacheManager::cacheImage(const QString &remotePath, const QByteArray &data, bool thumbnail) {
    QMutexLocker locker(&m_cacheMutex);

    QString cacheKey = generateCacheKey(remotePath, thumbnail);
    QString cachePath = m_cachePath + "/" + cacheKey;

    // 检查缓存空间
    while (m_currentCacheSize + data.size() > m_maxCacheSize && !m_accessTimes.isEmpty()) {
        evictLeastRecentlyUsed();
    }

    // 保存到缓存
    QFile file(cachePath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(data);
        file.close();

        m_accessTimes[cacheKey] = QDateTime::currentDateTime();
        updateCacheSize();

        emit cacheSizeChanged();
    }
}

void RemoteCacheManager::evictLeastRecentlyUsed() {
    auto oldest = m_accessTimes.begin();
    for (auto it = m_accessTimes.begin(); it != m_accessTimes.end(); ++it) {
        if (it.value() < oldest.value()) {
            oldest = it;
        }
    }

    QString cacheFile = m_cachePath + "/" + oldest.key();
    QFile::remove(cacheFile);
    m_accessTimes.erase(oldest);
}

// 7-2-2. 缓存路径迁移
void RemoteCacheManager::migrateCacheTo(const QString &newPath) {
    if (newPath == m_cachePath) return;

    QString oldPath = m_cachePath;

    // 创建新缓存目录
    QDir().mkpath(newPath);

    // 复制现有缓存文件
    QDir oldDir(oldPath);
    if (oldDir.exists()) {
        QStringList files = oldDir.entryList(QDir::Files);
        for (const QString &file : files) {
            QString oldFile = oldPath + "/" + file;
            QString newFile = newPath + "/" + file;
            QFile::copy(oldFile, newFile);
        }
    }

    // 更新缓存路径
    m_cachePath = newPath;

    // 清理旧缓存目录
    QDir(oldPath).removeRecursively();

    emit cacheMigrated(oldPath, newPath);
    emit cachePathChanged();
}
```

### 7-3. 渐进式图片加载
```cpp
// 7-3-1. 渐进式加载管理器
class ProgressiveImageLoader : public QObject {
    Q_OBJECT

public:
    enum LoadStage {
        Thumbnail,      // 缩略图（快速预览）
        LowQuality,     // 低质量版本（1/4分辨率）
        MediumQuality,  // 中等质量版本（1/2分辨率）
        FullQuality     // 完整质量版本
    };

    // 7-3-2. 渐进式加载接口
    Q_INVOKABLE void loadImageProgressively(const QString &remotePath, const QString &serverUrl);
    Q_INVOKABLE void cancelLoading(const QString &remotePath);

signals:
    void imageStageLoaded(const QString &remotePath, LoadStage stage, const QString &localPath);
    void loadingProgress(const QString &remotePath, int percentage);
    void loadingError(const QString &remotePath, const QString &error);

private slots:
    void onStageDownloaded();
    void onDownloadProgress(qint64 received, qint64 total);

private:
    struct LoadingTask {
        QString remotePath;
        QString serverUrl;
        LoadStage currentStage;
        QNetworkReply *activeReply;
        bool cancelled;
    };

    void loadNextStage(LoadingTask &task);
    QString getStageUrl(const QString &serverUrl, const QString &remotePath, LoadStage stage);

    QHash<QString, LoadingTask> m_loadingTasks;
    QNetworkAccessManager *m_networkManager;
    RemoteCacheManager *m_cacheManager;
};

// 7-3-3. 渐进式加载实现
void ProgressiveImageLoader::loadImageProgressively(const QString &remotePath, const QString &serverUrl) {
    LoadingTask task;
    task.remotePath = remotePath;
    task.serverUrl = serverUrl;
    task.currentStage = Thumbnail;
    task.activeReply = nullptr;
    task.cancelled = false;

    m_loadingTasks[remotePath] = task;
    loadNextStage(m_loadingTasks[remotePath]);
}

void ProgressiveImageLoader::loadNextStage(LoadingTask &task) {
    if (task.cancelled) return;

    // 检查缓存
    QString cachedPath = m_cacheManager->getCachedFullImage(task.remotePath);
    if (!cachedPath.isEmpty() && task.currentStage == FullQuality) {
        emit imageStageLoaded(task.remotePath, task.currentStage, cachedPath);
        return;
    }

    // 构建请求URL
    QString url = getStageUrl(task.serverUrl, task.remotePath, task.currentStage);

    QNetworkRequest request(url);
    request.setRawHeader("User-Agent", "ImageViewer/1.0");

    // 对于大图使用Range请求实现渐进式加载
    if (task.currentStage != Thumbnail) {
        // 根据阶段设置不同的Range范围
        qint64 rangeStart = 0;
        qint64 rangeEnd = -1;

        switch (task.currentStage) {
        case LowQuality:
            rangeEnd = 1024 * 100; // 前100KB
            break;
        case MediumQuality:
            rangeEnd = 1024 * 500; // 前500KB
            break;
        case FullQuality:
            // 完整文件，不设置Range
            break;
        }

        if (rangeEnd > 0) {
            request.setRawHeader("Range", QString("bytes=%1-%2").arg(rangeStart).arg(rangeEnd).toUtf8());
        }
    }

    task.activeReply = m_networkManager->get(request);

    connect(task.activeReply, &QNetworkReply::finished, this, &ProgressiveImageLoader::onStageDownloaded);
    connect(task.activeReply, &QNetworkReply::downloadProgress, this, &ProgressiveImageLoader::onDownloadProgress);
}
```

---

## 8. 开发实施计划

### 7-1. 第一阶段：基础服务器
- **7-1-1**：实现ImageNetworkEngine基础框架
- **7-1-2**：添加HTTP服务器和基本路由
- **7-1-3**：集成现有ImageProcessor功能

### 7-2. 第二阶段：并发控制
- **7-2-1**：实现请求队列和优先级管理
- **7-2-2**：添加带宽限制和流量控制
- **7-2-3**：优化缓存策略

### 7-3. 第三阶段：界面集成
- **7-3-1**：创建网络设置界面
- **7-3-2**：实现远程浏览功能
- **7-3-3**：添加状态监控和错误处理

### 7-4. 第四阶段：测试优化
- **7-4-1**：性能测试和调优
- **7-4-2**：安全测试和加固
- **7-4-3**：用户体验优化

---

## 8. 技术要点

### 8-1. 最佳实践应用
- **现代C++特性**：使用智能指针、RAII、移动语义
- **Qt网络框架**：充分利用Qt的异步网络能力
- **内存管理**：避免内存泄漏，及时释放大对象

### 8-2. 性能优化策略
- **零拷贝传输**：直接从缓存到网络
- **压缩传输**：根据客户端支持选择压缩格式
- **连接复用**：HTTP Keep-Alive支持

### 8-3. 错误处理
- **优雅降级**：服务异常时不影响本地功能
- **重试机制**：网络错误自动重试
- **日志记录**：详细的操作日志便于调试

---

## 9. 部署与维护

### 9-1. 配置文件
```json
{
    "network": {
        "enabled": false,
        "port": 8080,
        "maxConnections": 10,
        "bandwidthLimit": 12500000,
        "watchedFolders": []
    },
    "cache": {
        "thumbnailCacheSize": 100,
        "diskCacheEnabled": true,
        "preloadEnabled": true
    }
}
```

### 9-2. 监控指标
- **连接数**：当前活跃连接
- **传输速率**：实时带宽使用
- **缓存命中率**：缓存效率统计
- **错误率**：请求失败比例

---

## 结语

本文档基于现有ImageProcessor架构，采用最小化改动原则，通过扩展而非重构的方式实现网络共享功能。重点关注：

1. **代码复用**：最大化利用现有图片处理逻辑
2. **性能优先**：针对12.5MB/s网络限制优化
3. **简洁架构**：避免过度设计，保持代码精简
4. **渐进实施**：分阶段开发，降低风险

开发时严格按照编号顺序实施，每完成一个模块在文档开头标注进度。

---

## 10. 精简实现总结

### 10-1. 基于现有架构的最小化实现
**已完成的核心扩展：**

1. **NetworkBackend扩展** - 在现有网络后端基础上添加HTTP服务
   - 复用现有IP地址获取逻辑
   - 添加QHttpServer支持
   - 集成ImageProcessor引用

2. **ImageProcessor扩展** - 添加网络服务方法
   - 复用现有缓存机制
   - 利用现有文件列表和模型
   - 最小化并发控制

3. **QML界面** - 简单的网络设置对话框
   - 基于现有组件风格
   - 直接调用NetworkBackend方法

### 10-2. 实际代码架构
```
现有架构 + 最小扩展：

NetworkBackend (现有)
├── IP地址管理 (现有)
├── HTTP服务器 (新增)
├── 路由处理 (新增)
└── ImageProcessor集成 (新增)

ImageProcessor (现有)
├── 文件列表管理 (现有)
├── 缩略图缓存 (现有)
├── 网络API方法 (新增)
└── 并发控制 (简化新增)

QML界面
├── NetworkSettingsDialog (新增)
└── 集成到现有主界面 (待完成)
```

### 10-3. 关键特性
- **零冗余类**：直接扩展现有NetworkBackend和ImageProcessor
- **复用缓存**：利用现有thumbPathForFile和缩略图缓存
- **简化并发**：使用原子计数器而非复杂的队列系统
- **直传源文件**：不压缩，保持原始质量
- **Range支持**：实现渐进式加载

### 10-4. API端点
- `GET /api/images?path={folder}` - 图片列表（JSON格式）
- `GET /api/image/{path}` - 完整图片（支持Range请求）
- `GET /api/thumbnail/{path}` - 缩略图（JPEG格式）
- `OPTIONS /api/*` - CORS支持

### 10-5. 集成步骤
1. **编译** - CMakeLists.txt已更新，包含HttpServer
2. **注册** - 在main.cpp中设置ImageProcessor引用
3. **界面** - 将NetworkSettingsDialog集成到设置菜单
4. **测试** - 使用test_network_integration.cpp验证功能
```cpp
// ImageNetworkEngine.h
#ifndef IMAGENETWORKENGINE_H
#define IMAGENETWORKENGINE_H

#include <QObject>
#include <QHttpServer>
#include <QHttpServerResponse>
#include <QHttpServerRequest>
#include <QJsonObject>
#include <QJsonArray>
#include <QMutex>
#include <QSemaphore>
#include <QTimer>
#include <QNetworkAccessManager>
#include "ImageProcessor.h"

class ImageNetworkEngine : public QObject {
    Q_OBJECT
    Q_PROPERTY(bool serverRunning READ isServerRunning NOTIFY serverStatusChanged)
    Q_PROPERTY(int serverPort READ serverPort NOTIFY serverStatusChanged)
    Q_PROPERTY(QString serverAddress READ serverAddress NOTIFY serverStatusChanged)

public:
    static ImageNetworkEngine* instance();

    // 10-1-1. 服务器控制
    Q_INVOKABLE bool startServer(int port = 3333);
    Q_INVOKABLE void stopServer();
    Q_INVOKABLE void forceStopServer(); // 强制停止并释放所有资源
    Q_INVOKABLE bool isServerRunning() const { return m_serverRunning; }
    Q_INVOKABLE int serverPort() const { return m_port; }
    Q_INVOKABLE QString serverAddress() const;

    // 10-1-2. 文件夹管理
    Q_INVOKABLE void addWatchedFolder(const QString &path);
    Q_INVOKABLE void removeWatchedFolder(const QString &path);
    Q_INVOKABLE QStringList getWatchedFolders() const { return m_watchedFolders; }

signals:
    void serverStatusChanged();
    void requestReceived(const QString &clientIP, const QString &path);
    void bandwidthUsageChanged(qint64 bytesPerSecond);

private slots:
    void onBandwidthTimer();

private:
    explicit ImageNetworkEngine(QObject *parent = nullptr);
    ~ImageNetworkEngine();

    // 10-1-3. 路由处理
    void setupRoutes();
    QHttpServerResponse handleImageList(const QHttpServerRequest &request);
    QHttpServerResponse handleImageData(const QHttpServerRequest &request);
    QHttpServerResponse handleThumbnail(const QHttpServerRequest &request);
    QHttpServerResponse handleCORS(const QHttpServerRequest &request);

    // 10-1-4. 并发控制
    bool canProcessRequest(qint64 estimatedSize);
    void trackBandwidthUsage(qint64 bytes);

    // 10-1-5. 工具方法
    QString getClientIP(const QHttpServerRequest &request);
    QJsonObject createErrorResponse(const QString &error);
    QByteArray compressImage(const QByteArray &imageData, const QString &format);

private:
    static ImageNetworkEngine* s_instance;
    QHttpServer* m_server;
    ImageProcessor* m_imageProcessor;
    bool m_serverRunning;
    int m_port;
    QStringList m_watchedFolders;

    // 并发控制
    QSemaphore m_concurrencyLimiter;
    QMutex m_bandwidthMutex;
    qint64 m_bandwidthUsed;
    qint64 m_maxBandwidthPerSecond;
    QTimer* m_bandwidthTimer;

    // 请求统计
    QAtomicInt m_activeRequests;
    QAtomicLongLong m_totalRequests;
    QAtomicLongLong m_totalBytesServed;
};

#endif // IMAGENETWORKENGINE_H
```

### 10-2. 并发控制详细实现
```cpp
// ImageNetworkEngine.cpp 关键方法实现

bool ImageNetworkEngine::canProcessRequest(qint64 estimatedSize) {
    QMutexLocker locker(&m_bandwidthMutex);

    // 检查当前带宽使用情况
    if (m_bandwidthUsed + estimatedSize > m_maxBandwidthPerSecond) {
        // 如果是大文件（>12.5MB），需要等待
        if (estimatedSize > m_maxBandwidthPerSecond) {
            return m_activeRequests.load() == 0; // 只有没有其他请求时才处理大文件
        }
        return false; // 小文件也要等待带宽释放
    }

    // 检查并发数限制
    return m_concurrencyLimiter.tryAcquire();
}

void ImageNetworkEngine::trackBandwidthUsage(qint64 bytes) {
    QMutexLocker locker(&m_bandwidthMutex);
    m_bandwidthUsed += bytes;
    m_totalBytesServed.fetchAndAddOrdered(bytes);
    emit bandwidthUsageChanged(m_bandwidthUsed);
}

void ImageNetworkEngine::onBandwidthTimer() {
    QMutexLocker locker(&m_bandwidthMutex);
    m_bandwidthUsed = 0; // 每秒重置带宽计数
}

QHttpServerResponse ImageNetworkEngine::handleImageData(const QHttpServerRequest &request) {
    QString imagePath = request.url().path();
    imagePath = imagePath.mid(11); // 移除 "/api/image/" 前缀

    // 解码路径
    imagePath = QUrl::fromPercentEncoding(imagePath.toUtf8());

    // 安全检查：确保路径在监听文件夹内
    bool pathAllowed = false;
    for (const QString &watchedFolder : m_watchedFolders) {
        if (imagePath.startsWith(watchedFolder)) {
            pathAllowed = true;
            break;
        }
    }

    if (!pathAllowed) {
        return QHttpServerResponse(QHttpServerResponse::StatusCode::Forbidden,
                                 createErrorResponse("Path not allowed").toJson());
    }

    // 检查文件存在性
    QFileInfo fileInfo(imagePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        return QHttpServerResponse(QHttpServerResponse::StatusCode::NotFound,
                                 createErrorResponse("File not found").toJson());
    }

    // 估算文件大小并检查并发限制
    qint64 fileSize = fileInfo.size();
    if (!canProcessRequest(fileSize)) {
        return QHttpServerResponse(QHttpServerResponse::StatusCode::TooManyRequests,
                                 createErrorResponse("Server busy, please retry").toJson());
    }

    // 增加活跃请求计数
    m_activeRequests.fetchAndAddOrdered(1);

    // 读取文件数据
    QFile file(imagePath);
    if (!file.open(QIODevice::ReadOnly)) {
        m_activeRequests.fetchAndSubOrdered(1);
        m_concurrencyLimiter.release();
        return QHttpServerResponse(QHttpServerResponse::StatusCode::InternalServerError,
                                 createErrorResponse("Cannot read file").toJson());
    }

    QByteArray imageData = file.readAll();
    file.close();

    // 记录带宽使用
    trackBandwidthUsage(imageData.size());

    // 设置响应头
    QHttpServerResponse response(imageData);
    response.setHeader("Content-Type", "image/" + fileInfo.suffix().toLower());
    response.setHeader("Cache-Control", "public, max-age=3600");
    response.setHeader("Access-Control-Allow-Origin", "*");

    // 减少活跃请求计数并释放信号量
    m_activeRequests.fetchAndSubOrdered(1);
    m_concurrencyLimiter.release();

    return response;
}

// 10-1-5. 服务器停止处理
void ImageNetworkEngine::stopServer() {
    if (!m_serverRunning || !m_server) return;

    qInfo() << "Stopping network server...";

    // 标记服务器正在停止
    m_serverRunning = false;

    // 等待当前请求完成（最多等待5秒）
    int waitCount = 0;
    while (m_activeRequests.load() > 0 && waitCount < 50) {
        QThread::msleep(100);
        waitCount++;
    }

    // 强制停止服务器
    forceStopServer();

    emit serverStatusChanged();
    qInfo() << "Network server stopped gracefully";
}

void ImageNetworkEngine::forceStopServer() {
    if (!m_server) return;

    qInfo() << "Force stopping network server and releasing all resources...";

    // 停止带宽计时器
    if (m_bandwidthTimer) {
        m_bandwidthTimer->stop();
    }

    // 关闭服务器（这会自动断开所有连接并发送中断包）
    m_server->disconnect();
    delete m_server;
    m_server = nullptr;

    // 重置状态
    m_serverRunning = false;
    m_port = 0;
    m_activeRequests.store(0);

    // 清理并发控制
    while (m_concurrencyLimiter.available() < 10) {
        m_concurrencyLimiter.release();
    }

    // 重置带宽统计
    {
        QMutexLocker locker(&m_bandwidthMutex);
        m_bandwidthUsed = 0;
    }

    emit serverStatusChanged();
    qInfo() << "All server resources released, connections terminated";
}
```

### 10-3. QML网络设置界面（更新版）
```qml
// NetworkSettingsPage.qml
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1

Page {
    id: networkSettingsPage
    title: "网络共享设置"

    property alias serverEnabled: serverSwitch.checked
    property alias serverPort: portSpinBox.value

    ScrollView {
        anchors.fill: parent
        anchors.margins: 20

        ColumnLayout {
            width: parent.width
            spacing: 20

            // 10-3-1. 远程缓存设置区域（置顶）
            GroupBox {
                title: "远程缓存设置"
                Layout.fillWidth: true

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 15

                    RowLayout {
                        Label {
                            text: "缓存文件夹路径:"
                            Layout.preferredWidth: 120
                        }

                        TextField {
                            id: cachePathField
                            Layout.fillWidth: true
                            text: remoteCacheManager.cachePath || "cache/remote"
                            readOnly: true
                        }

                        Button {
                            text: "选择"
                            onClicked: cacheFolderDialog.open()
                        }

                        Button {
                            text: "清理缓存"
                            enabled: remoteCacheManager.cacheSize > 0
                            onClicked: clearCacheDialog.open()
                        }
                    }

                    RowLayout {
                        Label { text: "缓存使用情况:" }
                        Label {
                            text: remoteCacheManager.formatCacheSize() + " / " + formatBytes(remoteCacheManager.maxCacheSize)
                            color: remoteCacheManager.cacheSize > remoteCacheManager.maxCacheSize * 0.8 ? "orange" : "green"
                        }

                        ProgressBar {
                            Layout.fillWidth: true
                            value: remoteCacheManager.maxCacheSize > 0 ? remoteCacheManager.cacheSize / remoteCacheManager.maxCacheSize : 0
                            Layout.preferredHeight: 8
                        }
                    }

                    RowLayout {
                        Label { text: "缓存项目数:" }
                        Label { text: remoteCacheManager.cachedItemCount() + " 个文件" }
                    }
                }
            }

            // 10-3-2. 服务器控制区域
            GroupBox {
                title: "服务器设置"
                Layout.fillWidth: true

                ColumnLayout {
                    anchors.fill: parent

                    RowLayout {
                        Label { text: "启用网络服务:" }
                        Switch {
                            id: serverSwitch
                            onCheckedChanged: {
                                if (checked) {
                                    if (imageNetworkEngine.startServer(portSpinBox.value)) {
                                        statusLabel.text = "服务器已启动"
                                        statusLabel.color = "green"
                                    } else {
                                        checked = false
                                        statusLabel.text = "服务器启动失败"
                                        statusLabel.color = "red"
                                    }
                                } else {
                                    imageNetworkEngine.stopServer()
                                    statusLabel.text = "服务器已停止，所有连接已断开"
                                    statusLabel.color = "gray"
                                }
                            }
                        }
                    }

                    RowLayout {
                        Label { text: "端口:" }
                        SpinBox {
                            id: portSpinBox
                            from: 1024
                            to: 65535
                            value: 3333
                            enabled: !serverSwitch.checked
                        }
                    }

                    Label {
                        id: statusLabel
                        text: "服务器已停止"
                        color: "gray"
                        font.bold: true
                    }

                    Label {
                        text: imageNetworkEngine.serverRunning ?
                              "访问地址: http://" + getLocalIP() + ":" + imageNetworkEngine.serverPort :
                              "服务器未运行"
                        visible: imageNetworkEngine.serverRunning
                        color: "blue"

                        MouseArea {
                            anchors.fill: parent
                            onClicked: Qt.openUrlExternally("http://" + getLocalIP() + ":" + imageNetworkEngine.serverPort)
                        }
                    }
                }
            }

            // 10-3-3. 监听文件夹管理
            GroupBox {
                title: "监听文件夹"
                Layout.fillWidth: true

                ColumnLayout {
                    anchors.fill: parent

                    ListView {
                        id: foldersListView
                        Layout.fillWidth: true
                        Layout.preferredHeight: 150
                        model: imageNetworkEngine.getWatchedFolders()

                        delegate: Rectangle {
                            width: foldersListView.width
                            height: 40
                            color: index % 2 ? "#f0f0f0" : "white"

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 5

                                Label {
                                    text: modelData
                                    Layout.fillWidth: true
                                }

                                Button {
                                    text: "移除"
                                    onClicked: {
                                        imageNetworkEngine.removeWatchedFolder(modelData)
                                        foldersListView.model = imageNetworkEngine.getWatchedFolders()
                                    }
                                }
                            }
                        }
                    }

                    Button {
                        text: "添加文件夹"
                        onClicked: folderDialog.open()
                    }
                }
            }

            // 10-3-4. 状态监控
            GroupBox {
                title: "运行状态"
                Layout.fillWidth: true

                GridLayout {
                    anchors.fill: parent
                    columns: 2

                    Label { text: "活跃连接:" }
                    Label { text: imageNetworkEngine.activeRequests || "0" }

                    Label { text: "总请求数:" }
                    Label { text: imageNetworkEngine.totalRequests || "0" }

                    Label { text: "传输数据:" }
                    Label { text: formatBytes(imageNetworkEngine.totalBytesServed || 0) }

                    Label { text: "当前带宽:" }
                    Label {
                        text: formatBytes(imageNetworkEngine.bandwidthUsage || 0) + "/s"
                        color: (imageNetworkEngine.bandwidthUsage || 0) > 10000000 ? "red" : "green"
                    }

                    Label { text: "请求频率:" }
                    Label {
                        text: imageNetworkEngine.requestsPerSecond + " 请求/秒"
                        color: imageNetworkEngine.requestsPerSecond > 6 ? "orange" : "green"
                    }
                }
            }
        }
    }

    // 缓存文件夹选择对话框
    FolderDialog {
        id: cacheFolderDialog
        title: "选择缓存文件夹"
        onAccepted: {
            remoteCacheManager.migrateCacheTo(selectedFolder.toString().replace("file:///", ""))
            cachePathField.text = remoteCacheManager.cachePath
        }
    }

    // 监听文件夹选择对话框
    FolderDialog {
        id: folderDialog
        title: "选择监听文件夹"
        onAccepted: {
            imageNetworkEngine.addWatchedFolder(selectedFolder.toString().replace("file:///", ""))
            foldersListView.model = imageNetworkEngine.getWatchedFolders()
        }
    }

    // 清理缓存确认对话框
    Dialog {
        id: clearCacheDialog
        title: "清理缓存"
        modal: true
        anchors.centerIn: parent

        ColumnLayout {
            Label {
                text: "确定要清理所有远程缓存吗？\n这将删除 " + remoteCacheManager.formatCacheSize() + " 的缓存数据。"
                wrapMode: Text.WordWrap
            }

            RowLayout {
                Button {
                    text: "取消"
                    onClicked: clearCacheDialog.close()
                }

                Button {
                    text: "确定清理"
                    highlighted: true
                    onClicked: {
                        remoteCacheManager.clearCache()
                        clearCacheDialog.close()
                    }
                }
            }
        }
    }

    // 工具函数
    function getLocalIP() {
        // 这里需要实现获取本地IP的逻辑
        return "*************" // 示例IP
    }

    function formatBytes(bytes) {
        if (bytes === 0) return "0 B"
        const k = 1024
        const sizes = ["B", "KB", "MB", "GB"]
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
    }
}
```

### 10-4. 客户端远程浏览实现
```qml
// RemoteImageBrowser.qml
import QtQuick 2.15
import QtQuick.Controls 2.15

Item {
    id: remoteBrowser

    property string serverUrl: ""
    property string currentPath: ""

    // 10-4-1. 远程图片模型
    ListModel {
        id: remoteImageModel
    }

    ColumnLayout {
        anchors.fill: parent

        // 服务器连接区域
        RowLayout {
            Layout.fillWidth: true
            Layout.margins: 10

            Label { text: "服务器地址:" }
            TextField {
                id: serverUrlField
                Layout.fillWidth: true
                placeholderText: "http://*************:8080"
                text: serverUrl
            }

            Button {
                text: "连接"
                onClicked: connectToServer(serverUrlField.text)
            }
        }

        // 路径导航
        ScrollView {
            Layout.fillWidth: true
            Layout.preferredHeight: 40

            Row {
                id: breadcrumbRow
                spacing: 5

                Repeater {
                    model: currentPath.split("/").filter(function(part) { return part.length > 0 })

                    delegate: Row {
                        Button {
                            text: modelData
                            flat: true
                            onClicked: navigateToPath(getPathUpTo(index))
                        }
                        Label { text: "/" }
                    }
                }
            }
        }

        // 图片网格（复用现有imagePage组件结构）
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true

            GridView {
                id: imageGrid
                cellWidth: 180
                cellHeight: 180
                model: remoteImageModel

                delegate: Rectangle {
                    width: imageGrid.cellWidth - 10
                    height: imageGrid.cellHeight - 10
                    color: "#f0f0f0"
                    radius: 5

                    Column {
                        anchors.centerIn: parent
                        spacing: 5

                        Image {
                            width: 150
                            height: 120
                            source: model.thumbnailUrl
                            fillMode: Image.PreserveAspectFit
                            asynchronous: true

                            BusyIndicator {
                                anchors.centerIn: parent
                                running: parent.status === Image.Loading
                            }
                        }

                        Label {
                            text: model.name
                            width: 150
                            elide: Text.ElideMiddle
                            horizontalAlignment: Text.AlignHCenter
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        onDoubleClicked: {
                            if (model.isFolder) {
                                navigateToPath(currentPath + "/" + model.name)
                            } else {
                                openRemotePreview(model.fullImageUrl)
                            }
                        }
                    }
                }
            }
        }
    }

    // 10-4-2. 网络请求处理
    function connectToServer(url) {
        serverUrl = url
        loadImageList("")
    }

    function loadImageList(path) {
        const xhr = new XMLHttpRequest()
        xhr.open("GET", serverUrl + "/api/images?path=" + encodeURIComponent(path))
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText)
                    updateImageModel(response.images)
                    currentPath = path
                } else {
                    console.error("Failed to load images:", xhr.statusText)
                }
            }
        }
        xhr.send()
    }

    function updateImageModel(images) {
        remoteImageModel.clear()
        for (let i = 0; i < images.length; i++) {
            const image = images[i]
            remoteImageModel.append({
                name: image.name,
                isFolder: image.isFolder,
                thumbnailUrl: image.isFolder ? "" : serverUrl + "/api/thumbnail/" + encodeURIComponent(image.path),
                fullImageUrl: image.isFolder ? "" : serverUrl + "/api/image/" + encodeURIComponent(image.path)
            })
        }
    }

    function navigateToPath(path) {
        loadImageList(path)
    }

    function getPathUpTo(index) {
        const parts = currentPath.split("/").filter(function(part) { return part.length > 0 })
        return "/" + parts.slice(0, index + 1).join("/")
    }

    function openRemotePreview(imageUrl) {
        // 打开远程图片预览窗口
        const component = Qt.createComponent("RemoteImagePreview.qml")
        if (component.status === Component.Ready) {
            const window = component.createObject(remoteBrowser, {
                imageUrl: imageUrl,
                serverUrl: serverUrl
            })
            window.show()
        }
    }
}
```

### 10-5. 远程图片预览窗口（基于现有ImagePreviewWindow）
```qml
// RemoteImagePreviewWindow.qml - 扩展现有预览窗口支持远程图片
import QtQuick 2.15
import QtQuick.Controls 2.15

ImagePreviewWindow {
    id: remotePreviewWindow

    // 远程图片特有属性
    property string serverUrl: ""
    property string remotePath: ""
    property bool useProgressiveLoading: true

    // 重写加载函数支持远程图片
    function loadRemoteImage(path, serverUrl) {
        remotePath = path
        remotePreviewWindow.serverUrl = serverUrl

        if (useProgressiveLoading) {
            progressiveLoader.loadImageProgressively(path, serverUrl)
        } else {
            // 直接加载完整图片
            let fullImageUrl = serverUrl + "/api/image/" + encodeURIComponent(path)
            previewImage.source = fullImageUrl
        }
    }

    // 渐进式加载器
    ProgressiveImageLoader {
        id: progressiveLoader

        onImageStageLoaded: function(path, stage, localPath) {
            if (path === remotePath) {
                // 根据加载阶段更新图片源
                switch (stage) {
                case ProgressiveImageLoader.Thumbnail:
                    previewImage.source = "file:///" + localPath
                    break
                case ProgressiveImageLoader.LowQuality:
                    // 显示低质量版本，用户可以快速预览
                    previewImage.source = "file:///" + localPath
                    break
                case ProgressiveImageLoader.FullQuality:
                    // 最终高质量版本
                    previewImage.source = "file:///" + localPath
                    updateImageSize()
                    break
                }
            }
        }

        onLoadingProgress: function(path, percentage) {
            if (path === remotePath) {
                loadingProgressBar.value = percentage / 100.0
                loadingProgressBar.visible = percentage < 100
            }
        }

        onLoadingError: function(path, error) {
            if (path === remotePath) {
                previewImage._showErrorOverlay = true
                loadingProgressBar.visible = false
            }
        }
    }

    // 在原有预览窗口基础上添加加载进度条
    Rectangle {
        id: loadingProgressBar
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 4
        color: "#2196F3"
        visible: false

        property real value: 0.0

        Rectangle {
            anchors.left: parent.left
            anchors.top: parent.top
            anchors.bottom: parent.bottom
            width: parent.width * parent.value
            color: "#4CAF50"

            Behavior on width {
                NumberAnimation { duration: 200 }
            }
        }
    }

    // 网络状态指示器
    Rectangle {
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 10
        width: 20
        height: 20
        radius: 10
        color: progressiveLoader.isLoading ? "orange" : "green"
        visible: serverUrl !== ""

        Text {
            anchors.centerIn: parent
            text: progressiveLoader.isLoading ? "⟳" : "✓"
            color: "white"
            font.pixelSize: 12
        }

        SequentialAnimation on rotation {
            running: progressiveLoader.isLoading
            loops: Animation.Infinite
            NumberAnimation { to: 360; duration: 1000 }
        }
    }

    // 重写关闭函数，取消网络请求
    function closeWindow() {
        if (remotePath !== "") {
            progressiveLoader.cancelLoading(remotePath)
        }

        // 调用父类关闭函数
        opacity = 0
        closeTimer.restart()
    }
}
```

---

## 11. 性能优化与最佳实践

### 11-1. 内存管理优化
```cpp
// 11-1-1. 智能缓存管理
class SmartImageCache {
public:
    QByteArray getCachedImage(const QString &path, bool &found) {
        QMutexLocker locker(&m_mutex);
        auto it = m_cache.find(path);
        if (it != m_cache.end()) {
            // 更新访问时间
            it.value().lastAccess = QDateTime::currentDateTime();
            found = true;
            return it.value().data;
        }
        found = false;
        return QByteArray();
    }

    void cacheImage(const QString &path, const QByteArray &data) {
        QMutexLocker locker(&m_mutex);

        // 检查缓存大小限制
        while (m_currentSize + data.size() > m_maxSize && !m_cache.isEmpty()) {
            evictLeastRecentlyUsed();
        }

        CacheEntry entry;
        entry.data = data;
        entry.lastAccess = QDateTime::currentDateTime();
        entry.size = data.size();

        m_cache[path] = entry;
        m_currentSize += data.size();
    }

private:
    struct CacheEntry {
        QByteArray data;
        QDateTime lastAccess;
        qint64 size;
    };

    void evictLeastRecentlyUsed() {
        auto oldest = m_cache.begin();
        for (auto it = m_cache.begin(); it != m_cache.end(); ++it) {
            if (it.value().lastAccess < oldest.value().lastAccess) {
                oldest = it;
            }
        }
        m_currentSize -= oldest.value().size;
        m_cache.erase(oldest);
    }

    QHash<QString, CacheEntry> m_cache;
    QMutex m_mutex;
    qint64 m_currentSize = 0;
    qint64 m_maxSize = 100 * 1024 * 1024; // 100MB
};
```

### 11-2. 异步处理模式
```cpp
// 11-2-1. 异步图片处理
class AsyncImageProcessor : public QObject {
    Q_OBJECT

public:
    void processImageAsync(const QString &path, QObject *receiver, const char *slot) {
        QtConcurrent::run([this, path, receiver, slot]() {
            QByteArray result = processImageSync(path);
            QMetaObject::invokeMethod(receiver, slot, Qt::QueuedConnection,
                                    Q_ARG(QString, path), Q_ARG(QByteArray, result));
        });
    }

private:
    QByteArray processImageSync(const QString &path) {
        // 直接读取源文件，不进行额外压缩（用户已预先压缩）
        QFile file(path);
        if (!file.open(QIODevice::ReadOnly)) return QByteArray();

        QByteArray data = file.readAll();

        // 直接返回源文件数据，保持原始质量
        return data;
    }
};
```

### 11-3. 网络传输优化
```cpp
// 11-3-1. 分块传输支持
QHttpServerResponse ImageNetworkEngine::handleLargeImageData(const QHttpServerRequest &request) {
    QString imagePath = extractImagePath(request);
    QFileInfo fileInfo(imagePath);

    if (!validatePath(imagePath) || !fileInfo.exists()) {
        return createErrorResponse("File not found", 404);
    }

    qint64 fileSize = fileInfo.size();
    QString rangeHeader = request.value("Range");

    if (!rangeHeader.isEmpty() && rangeHeader.startsWith("bytes=")) {
        // 处理Range请求（断点续传支持）
        return handleRangeRequest(imagePath, rangeHeader, fileSize);
    }

    // 大文件使用流式传输
    if (fileSize > 10 * 1024 * 1024) { // 10MB以上
        return createStreamingResponse(imagePath, fileSize);
    }

    // 小文件直接返回
    return createDirectResponse(imagePath);
}

QHttpServerResponse ImageNetworkEngine::handleRangeRequest(const QString &path,
                                                          const QString &rangeHeader,
                                                          qint64 fileSize) {
    // 解析Range头：bytes=start-end
    QRegularExpression re(R"(bytes=(\d+)-(\d*))");
    QRegularExpressionMatch match = re.match(rangeHeader);

    if (!match.hasMatch()) {
        return createErrorResponse("Invalid Range header", 416);
    }

    qint64 start = match.captured(1).toLongLong();
    qint64 end = match.captured(2).isEmpty() ? fileSize - 1 : match.captured(2).toLongLong();

    if (start >= fileSize || end >= fileSize || start > end) {
        return createErrorResponse("Range not satisfiable", 416);
    }

    QFile file(path);
    if (!file.open(QIODevice::ReadOnly)) {
        return createErrorResponse("Cannot read file", 500);
    }

    file.seek(start);
    QByteArray data = file.read(end - start + 1);

    QHttpServerResponse response(data);
    response.setStatusCode(QHttpServerResponse::StatusCode::PartialContent);
    response.setHeader("Content-Range", QString("bytes %1-%2/%3").arg(start).arg(end).arg(fileSize));
    response.setHeader("Accept-Ranges", "bytes");
    response.setHeader("Content-Length", QString::number(data.size()));

    return response;
}
```

### 11-4. 错误处理与日志
```cpp
// 11-4-1. 统一错误处理
class NetworkErrorHandler {
public:
    static QHttpServerResponse handleError(const QString &error, int statusCode = 500) {
        QJsonObject errorObj;
        errorObj["error"] = error;
        errorObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        errorObj["status"] = statusCode;

        // 记录错误日志
        qWarning() << "Network Error:" << statusCode << error;

        QHttpServerResponse response(QJsonDocument(errorObj).toJson());
        response.setStatusCode(static_cast<QHttpServerResponse::StatusCode>(statusCode));
        response.setHeader("Content-Type", "application/json");

        return response;
    }

    static void logRequest(const QHttpServerRequest &request, int responseCode, qint64 responseSize) {
        QString logEntry = QString("[%1] %2 %3 - %4 %5 bytes")
                          .arg(QDateTime::currentDateTime().toString(Qt::ISODate))
                          .arg(request.remoteAddress().toString())
                          .arg(request.url().toString())
                          .arg(responseCode)
                          .arg(responseSize);

        qInfo() << logEntry;

        // 可选：写入日志文件
        static QFile logFile("network_access.log");
        if (!logFile.isOpen()) {
            logFile.open(QIODevice::WriteOnly | QIODevice::Append);
        }

        if (logFile.isOpen()) {
            logFile.write(logEntry.toUtf8() + "\n");
            logFile.flush();
        }
    }
};
```

---

## 12. 安全性增强

### 12-1. 访问控制
```cpp
// 12-1-1. IP白名单管理
class AccessController {
public:
    bool isIPAllowed(const QString &ip) {
        QMutexLocker locker(&m_mutex);

        // 检查黑名单
        if (m_blacklist.contains(ip)) return false;

        // 如果白名单为空，允许所有IP（局域网模式）
        if (m_whitelist.isEmpty()) {
            return isLocalNetwork(ip);
        }

        return m_whitelist.contains(ip);
    }

    void addToWhitelist(const QString &ip) {
        QMutexLocker locker(&m_mutex);
        m_whitelist.insert(ip);
    }

    void addToBlacklist(const QString &ip) {
        QMutexLocker locker(&m_mutex);
        m_blacklist.insert(ip);

        // 记录安全事件
        qWarning() << "IP blocked:" << ip;
    }

    bool checkRateLimit(const QString &ip) {
        QMutexLocker locker(&m_mutex);

        QDateTime now = QDateTime::currentDateTime();
        auto &requests = m_requestCounts[ip];

        // 清理过期记录（10秒前的请求）
        requests.erase(std::remove_if(requests.begin(), requests.end(),
                      [now](const QDateTime &time) {
                          return time.secsTo(now) > 10;
                      }), requests.end());

        // 检查请求频率（每10秒最多60个请求，正常用户点击不会达到此限制）
        if (requests.size() >= 60) {
            qWarning() << "Rate limit exceeded for IP:" << ip << "requests in 10s:" << requests.size();
            addToBlacklist(ip);
            return false;
        }

        requests.append(now);
        return true;
    }

private:
    bool isLocalNetwork(const QString &ip) {
        QHostAddress addr(ip);

        // 检查是否为本地网络地址
        return addr.isLoopback() ||
               (addr.protocol() == QAbstractSocket::IPv4Protocol &&
                (addr.toIPv4Address() & 0xFF000000) == 0xC0A80000) || // 192.168.x.x
               (addr.protocol() == QAbstractSocket::IPv4Protocol &&
                (addr.toIPv4Address() & 0xFF000000) == 0x0A000000);   // 10.x.x.x
    }

    QSet<QString> m_whitelist;
    QSet<QString> m_blacklist;
    QHash<QString, QList<QDateTime>> m_requestCounts;
    QMutex m_mutex;
};
```

### 12-2. 路径安全验证
```cpp
// 12-2-1. 安全路径检查
class PathValidator {
public:
    static bool isPathSafe(const QString &requestedPath, const QStringList &allowedPaths) {
        // 规范化路径
        QString normalizedPath = QDir::cleanPath(requestedPath);

        // 检查路径遍历攻击
        if (normalizedPath.contains("..") || normalizedPath.contains("//")) {
            qWarning() << "Path traversal attempt detected:" << requestedPath;
            return false;
        }

        // 检查是否在允许的路径内
        for (const QString &allowedPath : allowedPaths) {
            if (normalizedPath.startsWith(QDir::cleanPath(allowedPath))) {
                return true;
            }
        }

        return false;
    }

    static bool isImageFile(const QString &filePath) {
        static const QStringList imageExtensions = {
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "avif", "tiff", "svg"
        };

        QFileInfo fileInfo(filePath);
        QString extension = fileInfo.suffix().toLower();

        return imageExtensions.contains(extension);
    }

    static QString sanitizeFileName(const QString &fileName) {
        QString sanitized = fileName;

        // 移除危险字符
        sanitized.remove(QRegularExpression(R"([<>:"/\\|?*])"));

        // 限制长度
        if (sanitized.length() > 255) {
            sanitized = sanitized.left(255);
        }

        return sanitized;
    }
};
```

---

## 13. 配置管理系统

### 13-1. 配置文件结构
```cpp
// 13-1-1. 网络配置管理
class NetworkConfig {
public:
    struct ServerSettings {
        bool enabled = false;
        int port = 8080;
        QString bindAddress = "0.0.0.0";
        int maxConnections = 10;
        qint64 maxBandwidthBps = 12500000; // 12.5MB/s
        int requestTimeoutMs = 30000;
        bool enableCORS = true;
        bool enableRangeRequests = true;
    };

    struct SecuritySettings {
        bool enableWhitelist = false;
        QStringList allowedIPs;
        QStringList blockedIPs;
        int maxRequestsPerMinute = 100;
        bool logAllRequests = true;
        QString logFilePath = "network_access.log";
    };

    struct CacheSettings {
        bool enableMemoryCache = true;
        qint64 memoryCacheSizeMB = 100;
        bool enableDiskCache = true;
        QString diskCachePath = "cache/network";
        int cacheExpirationHours = 24;
    };

    static NetworkConfig* instance() {
        static NetworkConfig config;
        return &config;
    }

    void loadFromFile(const QString &filePath = "network_config.json") {
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly)) {
            qWarning() << "Cannot load config file, using defaults";
            return;
        }

        QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
        QJsonObject root = doc.object();

        // 加载服务器设置
        QJsonObject serverObj = root["server"].toObject();
        m_serverSettings.enabled = serverObj["enabled"].toBool();
        m_serverSettings.port = serverObj["port"].toInt(8080);
        m_serverSettings.bindAddress = serverObj["bindAddress"].toString("0.0.0.0");
        m_serverSettings.maxConnections = serverObj["maxConnections"].toInt(10);
        m_serverSettings.maxBandwidthBps = serverObj["maxBandwidthBps"].toVariant().toLongLong(12500000);

        // 加载安全设置
        QJsonObject securityObj = root["security"].toObject();
        m_securitySettings.enableWhitelist = securityObj["enableWhitelist"].toBool();
        QJsonArray allowedIPs = securityObj["allowedIPs"].toArray();
        for (const auto &ip : allowedIPs) {
            m_securitySettings.allowedIPs.append(ip.toString());
        }

        // 加载缓存设置
        QJsonObject cacheObj = root["cache"].toObject();
        m_cacheSettings.enableMemoryCache = cacheObj["enableMemoryCache"].toBool(true);
        m_cacheSettings.memoryCacheSizeMB = cacheObj["memoryCacheSizeMB"].toVariant().toLongLong(100);

        // 加载监听文件夹
        QJsonArray foldersArray = root["watchedFolders"].toArray();
        m_watchedFolders.clear();
        for (const auto &folder : foldersArray) {
            m_watchedFolders.append(folder.toString());
        }
    }

    void saveToFile(const QString &filePath = "network_config.json") {
        QJsonObject root;

        // 保存服务器设置
        QJsonObject serverObj;
        serverObj["enabled"] = m_serverSettings.enabled;
        serverObj["port"] = m_serverSettings.port;
        serverObj["bindAddress"] = m_serverSettings.bindAddress;
        serverObj["maxConnections"] = m_serverSettings.maxConnections;
        serverObj["maxBandwidthBps"] = QJsonValue::fromVariant(m_serverSettings.maxBandwidthBps);
        root["server"] = serverObj;

        // 保存安全设置
        QJsonObject securityObj;
        securityObj["enableWhitelist"] = m_securitySettings.enableWhitelist;
        QJsonArray allowedIPs;
        for (const QString &ip : m_securitySettings.allowedIPs) {
            allowedIPs.append(ip);
        }
        securityObj["allowedIPs"] = allowedIPs;
        root["security"] = securityObj;

        // 保存缓存设置
        QJsonObject cacheObj;
        cacheObj["enableMemoryCache"] = m_cacheSettings.enableMemoryCache;
        cacheObj["memoryCacheSizeMB"] = QJsonValue::fromVariant(m_cacheSettings.memoryCacheSizeMB);
        root["cache"] = cacheObj;

        // 保存监听文件夹
        QJsonArray foldersArray;
        for (const QString &folder : m_watchedFolders) {
            foldersArray.append(folder);
        }
        root["watchedFolders"] = foldersArray;

        QFile file(filePath);
        if (file.open(QIODevice::WriteOnly)) {
            file.write(QJsonDocument(root).toJson());
        }
    }

    // Getters
    const ServerSettings& serverSettings() const { return m_serverSettings; }
    const SecuritySettings& securitySettings() const { return m_securitySettings; }
    const CacheSettings& cacheSettings() const { return m_cacheSettings; }
    const QStringList& watchedFolders() const { return m_watchedFolders; }

    // Setters
    ServerSettings& serverSettings() { return m_serverSettings; }
    SecuritySettings& securitySettings() { return m_securitySettings; }
    CacheSettings& cacheSettings() { return m_cacheSettings; }
    QStringList& watchedFolders() { return m_watchedFolders; }

private:
    ServerSettings m_serverSettings;
    SecuritySettings m_securitySettings;
    CacheSettings m_cacheSettings;
    QStringList m_watchedFolders;
};
```

---

## 14. 测试与调试

### 14-1. 单元测试框架
```cpp
// 14-1-1. 网络功能测试
class NetworkEngineTest : public QObject {
    Q_OBJECT

private slots:
    void initTestCase() {
        m_engine = ImageNetworkEngine::instance();
        m_testPort = 18080; // 使用测试端口
    }

    void testServerStartStop() {
        // 测试服务器启动
        QVERIFY(m_engine->startServer(m_testPort));
        QVERIFY(m_engine->isServerRunning());
        QCOMPARE(m_engine->serverPort(), m_testPort);

        // 测试服务器停止
        m_engine->stopServer();
        QVERIFY(!m_engine->isServerRunning());
    }

    void testImageListAPI() {
        // 启动测试服务器
        QVERIFY(m_engine->startServer(m_testPort));

        // 添加测试文件夹
        QString testFolder = QDir::tempPath() + "/test_images";
        QDir().mkpath(testFolder);
        m_engine->addWatchedFolder(testFolder);

        // 创建测试图片
        createTestImage(testFolder + "/test.jpg");

        // 发送HTTP请求
        QNetworkAccessManager manager;
        QNetworkRequest request(QUrl(QString("http://localhost:%1/api/images?path=%2")
                                   .arg(m_testPort).arg(testFolder)));

        QNetworkReply *reply = manager.get(request);
        QEventLoop loop;
        connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
        loop.exec();

        // 验证响应
        QCOMPARE(reply->error(), QNetworkReply::NoError);

        QJsonDocument doc = QJsonDocument::fromJson(reply->readAll());
        QJsonObject response = doc.object();
        QVERIFY(response.contains("images"));

        QJsonArray images = response["images"].toArray();
        QVERIFY(images.size() > 0);

        reply->deleteLater();
        m_engine->stopServer();
    }

    void testConcurrencyControl() {
        QVERIFY(m_engine->startServer(m_testPort));

        // 创建多个并发请求
        QList<QNetworkReply*> replies;
        QNetworkAccessManager manager;

        for (int i = 0; i < 20; ++i) {
            QNetworkRequest request(QUrl(QString("http://localhost:%1/api/images")
                                       .arg(m_testPort)));
            replies.append(manager.get(request));
        }

        // 等待所有请求完成
        for (auto reply : replies) {
            QEventLoop loop;
            connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
            if (!reply->isFinished()) {
                loop.exec();
            }

            // 验证没有服务器过载错误
            QVERIFY(reply->error() != QNetworkReply::ServiceUnavailableError);
            reply->deleteLater();
        }

        m_engine->stopServer();
    }

private:
    void createTestImage(const QString &path) {
        QImage image(100, 100, QImage::Format_RGB32);
        image.fill(Qt::blue);
        image.save(path, "JPEG");
    }

    ImageNetworkEngine *m_engine;
    int m_testPort;
};
```

### 14-2. 性能基准测试
```cpp
// 14-2-1. 性能测试工具
class PerformanceBenchmark {
public:
    struct BenchmarkResult {
        qint64 totalRequests = 0;
        qint64 successfulRequests = 0;
        qint64 failedRequests = 0;
        double averageResponseTime = 0.0;
        double requestsPerSecond = 0.0;
        qint64 totalBytesTransferred = 0;
        double averageThroughput = 0.0; // MB/s
    };

    static BenchmarkResult runLoadTest(const QString &serverUrl,
                                     int concurrentUsers,
                                     int requestsPerUser,
                                     int testDurationSeconds) {
        BenchmarkResult result;
        QElapsedTimer timer;
        timer.start();

        QList<QThread*> threads;
        QList<LoadTestWorker*> workers;

        // 创建并发测试线程
        for (int i = 0; i < concurrentUsers; ++i) {
            QThread *thread = new QThread;
            LoadTestWorker *worker = new LoadTestWorker(serverUrl, requestsPerUser);
            worker->moveToThread(thread);

            connect(thread, &QThread::started, worker, &LoadTestWorker::start);
            connect(worker, &LoadTestWorker::finished, thread, &QThread::quit);
            connect(thread, &QThread::finished, thread, &QThread::deleteLater);

            threads.append(thread);
            workers.append(worker);
            thread->start();
        }

        // 等待测试完成或超时
        QTimer::singleShot(testDurationSeconds * 1000, [&threads]() {
            for (auto thread : threads) {
                thread->quit();
            }
        });

        // 等待所有线程完成
        for (auto thread : threads) {
            thread->wait();
        }

        // 收集结果
        qint64 totalTime = timer.elapsed();
        for (auto worker : workers) {
            result.totalRequests += worker->totalRequests();
            result.successfulRequests += worker->successfulRequests();
            result.failedRequests += worker->failedRequests();
            result.totalBytesTransferred += worker->bytesTransferred();
        }

        result.averageResponseTime = totalTime / static_cast<double>(result.totalRequests);
        result.requestsPerSecond = result.totalRequests * 1000.0 / totalTime;
        result.averageThroughput = result.totalBytesTransferred / (1024.0 * 1024.0) / (totalTime / 1000.0);

        // 清理
        for (auto worker : workers) {
            worker->deleteLater();
        }

        return result;
    }

    static void printBenchmarkResult(const BenchmarkResult &result) {
        qInfo() << "=== Performance Benchmark Results ===";
        qInfo() << "Total Requests:" << result.totalRequests;
        qInfo() << "Successful Requests:" << result.successfulRequests;
        qInfo() << "Failed Requests:" << result.failedRequests;
        qInfo() << "Success Rate:" << (result.successfulRequests * 100.0 / result.totalRequests) << "%";
        qInfo() << "Average Response Time:" << result.averageResponseTime << "ms";
        qInfo() << "Requests per Second:" << result.requestsPerSecond;
        qInfo() << "Total Data Transferred:" << (result.totalBytesTransferred / 1024.0 / 1024.0) << "MB";
        qInfo() << "Average Throughput:" << result.averageThroughput << "MB/s";
    }
};

class LoadTestWorker : public QObject {
    Q_OBJECT

public:
    LoadTestWorker(const QString &serverUrl, int requestCount)
        : m_serverUrl(serverUrl), m_requestCount(requestCount) {}

    int totalRequests() const { return m_totalRequests; }
    int successfulRequests() const { return m_successfulRequests; }
    int failedRequests() const { return m_failedRequests; }
    qint64 bytesTransferred() const { return m_bytesTransferred; }

public slots:
    void start() {
        QNetworkAccessManager manager;

        for (int i = 0; i < m_requestCount; ++i) {
            QNetworkRequest request(QUrl(m_serverUrl + "/api/images"));
            QNetworkReply *reply = manager.get(request);

            QEventLoop loop;
            connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
            loop.exec();

            m_totalRequests++;
            if (reply->error() == QNetworkReply::NoError) {
                m_successfulRequests++;
                m_bytesTransferred += reply->bytesAvailable();
            } else {
                m_failedRequests++;
            }

            reply->deleteLater();

            // 短暂延迟模拟真实用户行为
            QThread::msleep(100);
        }

        emit finished();
    }

signals:
    void finished();

private:
    QString m_serverUrl;
    int m_requestCount;
    int m_totalRequests = 0;
    int m_successfulRequests = 0;
    int m_failedRequests = 0;
    qint64 m_bytesTransferred = 0;
};
```

---

## 15. 部署与集成指南

### 15-1. 项目集成步骤
```cmake
# 15-1-1. CMakeLists.txt 添加网络模块
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network HttpServer)

# 添加网络相关源文件
set(NETWORK_SOURCES
    backend/ImageNetworkEngine.cpp
    backend/ImageNetworkEngine.h
    backend/NetworkConfig.cpp
    backend/NetworkConfig.h
    backend/AccessController.cpp
    backend/AccessController.h
    backend/SmartImageCache.cpp
    backend/SmartImageCache.h
)

# 链接网络库
target_link_libraries(${PROJECT_NAME}
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
    Qt6::HttpServer
)

# 添加网络模块到主程序
target_sources(${PROJECT_NAME} PRIVATE ${NETWORK_SOURCES})
```

### 15-2. 主程序集成
```cpp
// 15-2-1. main.cpp 修改
#include "ImageNetworkEngine.h"
#include "NetworkConfig.h"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // 初始化网络配置
    NetworkConfig::instance()->loadFromFile();

    // 注册网络引擎到QML
    qmlRegisterSingletonType<ImageNetworkEngine>("ImageNetwork", 1, 0, "ImageNetworkEngine",
        [](QQmlEngine *engine, QJSEngine *scriptEngine) -> QObject * {
            Q_UNUSED(engine)
            Q_UNUSED(scriptEngine)
            return ImageNetworkEngine::instance();
        });

    // 现有的QML引擎初始化代码...
    QQmlApplicationEngine engine;
    engine.load(QUrl(QStringLiteral("qrc:/main.qml")));

    // 程序退出时保存配置
    QObject::connect(&app, &QApplication::aboutToQuit, []() {
        ImageNetworkEngine::instance()->stopServer();
        NetworkConfig::instance()->saveToFile();
    });

    return app.exec();
}
```

### 15-3. QML主界面集成
```qml
// 15-3-1. main.qml 添加网络设置入口
import QtQuick 2.15
import QtQuick.Controls 2.15
import ImageNetwork 1.0

ApplicationWindow {
    id: window

    // 现有代码...

    menuBar: MenuBar {
        Menu {
            title: "设置"
            MenuItem {
                text: "网络共享"
                onTriggered: networkSettingsDialog.open()
            }
            // 其他菜单项...
        }
    }

    // 网络设置对话框
    Dialog {
        id: networkSettingsDialog
        title: "网络共享设置"
        width: 600
        height: 500
        modal: true

        NetworkSettingsPage {
            anchors.fill: parent
        }
    }

    // 状态栏显示网络状态
    footer: ToolBar {
        RowLayout {
            anchors.fill: parent

            // 现有状态信息...

            Label {
                text: ImageNetworkEngine.serverRunning ?
                      "网络服务: 运行中 (" + ImageNetworkEngine.serverPort + ")" :
                      "网络服务: 已停止"
                color: ImageNetworkEngine.serverRunning ? "green" : "gray"
            }

            // 网络状态指示器
            Rectangle {
                width: 12
                height: 12
                radius: 6
                color: ImageNetworkEngine.serverRunning ? "green" : "gray"

                SequentialAnimation on opacity {
                    running: ImageNetworkEngine.serverRunning
                    loops: Animation.Infinite
                    NumberAnimation { to: 0.3; duration: 1000 }
                    NumberAnimation { to: 1.0; duration: 1000 }
                }
            }
        }
    }
}
```

### 15-4. 现有ImageProcessor扩展
```cpp
// 15-4-1. ImageProcessor.h 添加网络支持方法
class ImageProcessor : public QObject {
    // 现有代码...

public:
    // 新增网络服务相关方法
    Q_INVOKABLE QJsonObject getImageListForNetwork(const QString &folderPath);
    Q_INVOKABLE QByteArray getThumbnailDataForNetwork(int index);
    Q_INVOKABLE QByteArray getFullImageDataForNetwork(int index);
    Q_INVOKABLE QString getImagePathByIndex(int index);

    // 网络优化的缓存方法
    Q_INVOKABLE bool preloadThumbnailsForNetwork(int startIndex, int count);
    Q_INVOKABLE void clearNetworkCache();

signals:
    // 网络相关信号
    void networkCacheUpdated(int cachedCount);
    void networkRequestProcessed(const QString &path, qint64 size);
};

// ImageProcessor.cpp 实现
QJsonObject ImageProcessor::getImageListForNetwork(const QString &folderPath) {
    QJsonObject result;
    QJsonArray images;

    // 设置当前路径
    setCurrentPath(folderPath);

    // 遍历当前文件列表
    for (int i = 0; i < m_fileInfoList.count(); ++i) {
        const QFileInfo &fileInfo = m_fileInfoList.at(i);

        QJsonObject imageObj;
        imageObj["name"] = fileInfo.fileName();
        imageObj["isFolder"] = fileInfo.isDir();
        imageObj["path"] = fileInfo.absoluteFilePath();
        imageObj["size"] = fileInfo.size();
        imageObj["lastModified"] = fileInfo.lastModified().toString(Qt::ISODate);

        if (!fileInfo.isDir()) {
            // 为图片文件添加额外信息
            imageObj["extension"] = fileInfo.suffix().toLower();
            imageObj["thumbnailAvailable"] = QFile::exists(thumbPathForFile(fileInfo.absoluteFilePath()));
        }

        images.append(imageObj);
    }

    result["images"] = images;
    result["totalCount"] = images.size();
    result["currentPath"] = folderPath;
    result["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return result;
}

QByteArray ImageProcessor::getThumbnailDataForNetwork(int index) {
    if (index < 0 || index >= m_fileInfoList.count()) {
        return QByteArray();
    }

    const QString filePath = m_fileInfoList.at(index).absoluteFilePath();
    const QString thumbPath = thumbPathForFile(filePath);

    // 优先返回缓存的缩略图
    if (QFile::exists(thumbPath)) {
        QFile file(thumbPath);
        if (file.open(QIODevice::ReadOnly)) {
            return file.readAll();
        }
    }

    // 如果没有缓存，生成缩略图
    QImage thumbnail = generateThumbnail(filePath);
    if (!thumbnail.isNull()) {
        QByteArray data;
        QBuffer buffer(&data);
        buffer.open(QIODevice::WriteOnly);
        thumbnail.save(&buffer, "JPEG", 85);

        // 异步保存到缓存
        QtConcurrent::run([thumbPath, data]() {
            QFile file(thumbPath);
            if (file.open(QIODevice::WriteOnly)) {
                file.write(data);
            }
        });

        return data;
    }

    return QByteArray();
}
```

---

## 16. 最佳实践总结

### 16-1. 代码架构原则
1. **单一职责**：每个类专注于特定功能
2. **最小化依赖**：减少模块间耦合
3. **资源管理**：使用RAII和智能指针
4. **异步优先**：避免阻塞主线程
5. **错误处理**：完善的异常和错误处理机制

### 16-2. 性能优化要点
```cpp
// 16-2-1. 关键性能优化技巧
class PerformanceOptimizer {
public:
    // 内存池管理
    static QByteArray* getBuffer(qint64 size) {
        static thread_local QHash<qint64, QStack<QByteArray*>> bufferPools;

        auto& pool = bufferPools[size];
        if (pool.isEmpty()) {
            return new QByteArray();
        }

        QByteArray* buffer = pool.pop();
        buffer->resize(size);
        return buffer;
    }

    static void returnBuffer(QByteArray* buffer) {
        static thread_local QHash<qint64, QStack<QByteArray*>> bufferPools;

        qint64 size = buffer->capacity();
        auto& pool = bufferPools[size];

        if (pool.size() < 10) { // 限制池大小
            buffer->clear();
            pool.push(buffer);
        } else {
            delete buffer;
        }
    }

    // 零拷贝数据传输
    static QHttpServerResponse createZeroCopyResponse(const QString& filePath) {
        QFile* file = new QFile(filePath);
        if (!file->open(QIODevice::ReadOnly)) {
            delete file;
            return QHttpServerResponse(QHttpServerResponse::StatusCode::NotFound);
        }

        // 使用QIODevice直接传输，避免加载到内存
        QHttpServerResponse response;
        response.setStatusCode(QHttpServerResponse::StatusCode::Ok);
        response.setHeader("Content-Type", "application/octet-stream");
        response.setHeader("Content-Length", QString::number(file->size()));

        // 注意：这里需要确保file在响应完成后被正确删除
        return response;
    }
};
```

### 16-3. 安全最佳实践
1. **输入验证**：严格验证所有用户输入
2. **路径安全**：防止目录遍历攻击
3. **访问控制**：实现IP白名单和频率限制
4. **日志记录**：记录所有安全相关事件
5. **错误信息**：避免泄露敏感信息

### 16-4. 监控与维护
```cpp
// 16-4-1. 系统监控指标
struct SystemMetrics {
    // 性能指标
    qint64 totalRequests = 0;
    qint64 activeConnections = 0;
    double averageResponseTime = 0.0;
    qint64 bandwidthUsage = 0;

    // 资源使用
    qint64 memoryUsage = 0;
    double cpuUsage = 0.0;
    qint64 diskIORate = 0;

    // 错误统计
    qint64 errorCount = 0;
    qint64 timeoutCount = 0;
    qint64 rejectedRequests = 0;

    QDateTime lastUpdated = QDateTime::currentDateTime();
};

class SystemMonitor : public QObject {
    Q_OBJECT

public:
    static SystemMonitor* instance() {
        static SystemMonitor monitor;
        return &monitor;
    }

    const SystemMetrics& currentMetrics() const { return m_metrics; }

    void recordRequest(qint64 responseTime, qint64 dataSize) {
        QMutexLocker locker(&m_mutex);
        m_metrics.totalRequests++;

        // 更新平均响应时间（移动平均）
        m_metrics.averageResponseTime = (m_metrics.averageResponseTime * 0.9) + (responseTime * 0.1);

        // 更新带宽使用
        m_metrics.bandwidthUsage += dataSize;

        emit metricsUpdated(m_metrics);
    }

    void recordError(const QString& errorType) {
        QMutexLocker locker(&m_mutex);
        m_metrics.errorCount++;

        qWarning() << "System error recorded:" << errorType;
        emit errorRecorded(errorType);
    }

signals:
    void metricsUpdated(const SystemMetrics& metrics);
    void errorRecorded(const QString& errorType);

private:
    SystemMetrics m_metrics;
    QMutex m_mutex;
};
```

---

## 17. 开发检查清单

### 17-1. 功能完成度检查
- [ ] **1-1** ImageNetworkEngine基础框架
- [ ] **1-2** 服务器启动/停止功能
- [ ] **2-1** HTTP路由和请求处理
- [ ] **2-2** 图片列表API
- [ ] **2-3** 图片数据传输API
- [ ] **3-1** 并发控制机制
- [ ] **3-2** 带宽限制实现
- [ ] **3-3** 缓存策略优化
- [ ] **4-1** QML网络设置界面
- [ ] **4-2** 远程浏览功能
- [ ] **5-1** 安全访问控制
- [ ] **5-2** 错误处理机制
- [ ] **6-1** 配置管理系统
- [ ] **6-2** 日志记录功能
- [ ] **7-1** 单元测试覆盖
- [ ] **7-2** 性能基准测试
- [ ] **8-1** 文档完整性
- [ ] **8-2** 部署指南

### 17-2. 质量保证检查
- [ ] **内存管理**：无内存泄漏，正确使用智能指针
- [ ] **线程安全**：所有共享数据都有适当的同步机制
- [ ] **异常处理**：所有可能的异常都被正确捕获和处理
- [ ] **资源清理**：程序退出时正确清理所有资源
- [ ] **性能优化**：满足12.5MB/s带宽限制要求
- [ ] **安全性**：通过安全测试，无明显漏洞
- [ ] **兼容性**：在目标平台上正常运行
- [ ] **用户体验**：界面响应流畅，操作直观

### 17-3. 部署前检查
- [ ] **配置文件**：默认配置合理，文档完整
- [ ] **依赖库**：所有依赖都已正确链接
- [ ] **权限设置**：网络权限配置正确
- [ ] **防火墙**：端口访问规则配置
- [ ] **日志目录**：日志文件路径可写
- [ ] **缓存目录**：缓存目录权限正确
- [ ] **测试环境**：在真实网络环境中测试
- [ ] **文档更新**：用户手册和技术文档最新

---

## 结语

本文档提供了完整的IP网络共享功能开发指南，基于以下核心原则：

### 技术特点
1. **最小化架构**：避免过度设计，保持代码简洁
2. **性能优先**：针对12.5MB/s网络限制优化，智能并发控制
3. **源文件直传**：不进行额外压缩，保持原始图片质量
4. **渐进式加载**：支持大图分块预览，提升用户体验
5. **本地缓存**：智能缓存管理，减少重复网络传输
6. **安全可靠**：完善的安全机制和错误处理
7. **资源管理**：服务停止时正确释放所有资源和连接

### 实施建议
1. **分阶段开发**：严格按照编号顺序实施
2. **持续测试**：每个阶段完成后进行充分测试
3. **性能监控**：实时监控系统性能和资源使用
4. **缓存管理**：合理设置缓存大小和清理策略
5. **网络优化**：利用Range请求实现断点续传
6. **安全审计**：定期进行安全检查和漏洞扫描

### 核心优化点
1. **端口设置**：默认使用3333端口，避免常用端口冲突
2. **频率限制**：每10秒60次请求限制，适合正常用户操作
3. **缓存策略**：缩略图永久缓存，原图LRU策略管理
4. **连接管理**：服务停止时发送中断包，优雅断开连接
5. **渐进式预览**：大图先显示低质量版本，再逐步加载高清版本

### 扩展方向
1. **协议支持**：未来可扩展WebSocket实时通信
2. **认证机制**：可添加用户认证和权限管理
3. **集群支持**：支持多服务器负载均衡
4. **移动端**：开发配套的移动客户端
5. **云同步**：集成云存储服务

通过遵循本文档的设计和实现指南，可以构建一个高性能、安全可靠的图片网络共享系统，满足用户的远程访问需求，同时保持原始图片质量和良好的用户体验。
```
