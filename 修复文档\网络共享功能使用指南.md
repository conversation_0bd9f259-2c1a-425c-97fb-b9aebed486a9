# 网络共享功能使用指南

## 快速开始

### 1. 编译要求
确保CMakeLists.txt包含以下Qt组件：
```cmake
find_package(Qt6 REQUIRED COMPONENTS Core Gui Quick Concurrent Network HttpServer)
target_link_libraries(your_target Qt6::Network Qt6::HttpServer)
```

### 2. 代码集成
在main.cpp中添加：
```cpp
#include "backend/NetworkBackend.h"

// 创建组件
NetworkBackend networkBackend;
ImageProcessor imageProcessor;

// 关键：设置引用
networkBackend.setImageProcessor(&imageProcessor);

// 注册到QML
engine.rootContext()->setContextProperty("networkBackend", &networkBackend);
```

### 3. QML界面集成
在主界面添加网络设置入口：
```qml
MenuItem {
    text: "网络共享"
    onTriggered: networkSettingsDialog.open()
}

NetworkSettingsDialog {
    id: networkSettingsDialog
}
```

## 功能特性

### HTTP API端点
- **图片列表**: `GET /api/images?path=文件夹路径`
- **完整图片**: `GET /api/image/图片路径` (支持Range请求)
- **缩略图**: `GET /api/thumbnail/图片路径`

### 使用示例
```bash
# 获取图片列表
curl "http://192.168.1.100:3333/api/images?path=/home/<USER>/pictures"

# 获取完整图片
curl "http://192.168.1.100:3333/api/image/home/<USER>/pictures/photo.jpg"

# 获取缩略图
curl "http://192.168.1.100:3333/api/thumbnail/home/<USER>/pictures/photo.jpg"

# Range请求（渐进式加载）
curl -H "Range: bytes=0-1023" "http://192.168.1.100:3333/api/image/photo.jpg"
```

### 响应格式
图片列表JSON示例：
```json
{
  "images": [
    {
      "name": "photo.jpg",
      "path": "/full/path/photo.jpg",
      "size": 1024000,
      "isFolder": false,
      "extension": "jpg",
      "hasThumbnail": true,
      "lastModified": "2024-01-01T12:00:00"
    }
  ],
  "totalCount": 1,
  "currentPath": "/home/<USER>/pictures",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 安全注意事项

### 网络安全
- 仅在可信网络环境中使用
- 默认允许所有来源访问（CORS: *）
- 建议在防火墙后使用

### 文件安全
- 自动验证文件路径，防止目录遍历
- 仅允许访问图片文件
- 检查文件存在性和可读性

## 性能特性

### 缓存机制
- 复用现有缩略图缓存
- 自动生成缺失的缩略图
- 异步缓存保存，不阻塞响应

### 并发控制
- 简单的原子计数器控制活跃请求
- 避免服务器过载
- 支持Range请求实现渐进式加载

### 带宽优化
- 直接传输源文件，无额外压缩
- 支持HTTP Range请求
- 缩略图使用JPEG格式，质量85%

## 故障排除

### 常见问题
1. **服务启动失败**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 验证Qt HttpServer组件是否正确链接

2. **无法访问图片**
   - 确认文件路径正确
   - 检查文件权限
   - 验证文件格式是否支持

3. **缩略图不显示**
   - 检查缩略图缓存目录权限
   - 确认ImageProcessor正确初始化
   - 验证图片文件完整性

### 调试方法
```cpp
// 启用详细日志
QLoggingCategory::setFilterRules("*.debug=true");

// 检查服务状态
qDebug() << "Server running:" << networkBackend.isServerRunning();
qDebug() << "Server URL:" << networkBackend.serverUrl();
qDebug() << "Active requests:" << networkBackend.activeRequests();
```

## 扩展开发

### 添加新API端点
在NetworkBackend::setupImageRoutes()中添加：
```cpp
m_httpServer->route("/api/custom", QHttpServerRequest::Method::Get,
    [this](const QHttpServerRequest &request) {
        return handleCustomRequest(request);
    });
```

### 自定义响应格式
扩展ImageProcessor::getNetworkImageList()方法，添加更多元数据。

### 增强安全性
- 添加IP白名单检查
- 实现请求频率限制
- 添加访问日志记录

## 最佳实践

1. **资源管理**: 程序退出时调用stopImageServer()
2. **错误处理**: 监听serverStatusChanged信号
3. **性能监控**: 定期检查activeRequests数量
4. **用户体验**: 在UI中显示服务状态和访问地址
5. **网络配置**: 根据实际网络环境调整端口设置
