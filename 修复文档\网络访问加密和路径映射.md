# 网络访问加密和路径映射完成

## 🔐 **访问加密机制**

### 🎯 **访问令牌系统**
- ✅ **自动生成令牌**：应用启动时自动生成32字节随机令牌
- ✅ **SHA256加密**：使用时间戳和随机数生成安全哈希
- ✅ **强制验证**：所有API请求必须包含有效令牌
- ✅ **UI显示**：设置页面显示当前令牌，支持一键复制

### 🔗 **新的访问URL格式**

#### **图片文件访问**
对于图片 `C:/Users/<USER>/Pictures/Saved Pictures/wallhaven-5gvok7.webp`：

```
http://127.0.0.1:3333/api/image/wallhaven-5gvok7.webp?token=你的访问令牌
```

#### **文件夹列表访问**
```
# 根目录
http://127.0.0.1:3333/api/images?path=/&token=你的访问令牌

# 子文件夹
http://127.0.0.1:3333/api/images?path=/子文件夹名&token=你的访问令牌
```

#### **缩略图访问**
```
http://127.0.0.1:3333/api/thumbnail/wallhaven-5gvok7.webp?token=你的访问令牌
```

## 🗂️ **路径映射系统**

### 📋 **客户端视图**
- ✅ **根目录显示**：监听文件夹在客户端显示为 `/`
- ✅ **面包屑导航**：子文件夹显示为 `/子文件夹名`
- ✅ **简化路径**：隐藏服务器真实路径，提高安全性

### 🔄 **路径映射逻辑**

#### **客户端 → 服务器**
```cpp
QString NetworkBackend::mapClientPathToServer(const QString& clientPath) {
    // "/" → "C:/Users/<USER>/Pictures/Saved Pictures"
    // "/子文件夹" → "C:/Users/<USER>/Pictures/Saved Pictures/子文件夹"
    // "/图片.jpg" → "C:/Users/<USER>/Pictures/Saved Pictures/图片.jpg"
}
```

#### **服务器 → 客户端**
```cpp
QString NetworkBackend::mapServerPathToClient(const QString& serverPath) {
    // "C:/Users/<USER>/Pictures/Saved Pictures" → "/"
    // "C:/Users/<USER>/Pictures/Saved Pictures/子文件夹" → "/子文件夹"
    // "C:/Users/<USER>/Pictures/Saved Pictures/图片.jpg" → "/图片.jpg"
}
```

## 🛡️ **安全特性**

### 🔐 **访问控制**
- ✅ **令牌验证**：无效令牌返回401 Unauthorized
- ✅ **路径隐藏**：客户端无法看到服务器真实路径
- ✅ **监听文件夹限制**：只能访问第一个监听文件夹内容
- ✅ **路径验证**：防止目录遍历攻击

### 📊 **错误响应**
```json
// 无效令牌 (401 Unauthorized)
{
  "error": "Unauthorized",
  "timestamp": "2025-07-09T21:00:00"
}

// 访问被拒绝 (403 Forbidden)
{
  "error": "Access denied",
  "timestamp": "2025-07-09T21:00:00"
}
```

## 🎮 **使用指南**

### 1. **获取访问令牌**
1. 打开设置页面
2. 在"访问令牌"卡片中查看当前令牌
3. 点击"复制"按钮复制令牌

### 2. **测试API访问**
```bash
# 获取根目录内容
curl "http://127.0.0.1:3333/api/images?path=/&token=你的令牌"

# 获取图片文件
curl "http://127.0.0.1:3333/api/image/wallhaven-5gvok7.webp?token=你的令牌"

# 获取缩略图
curl "http://127.0.0.1:3333/api/thumbnail/wallhaven-5gvok7.webp?token=你的令牌"
```

### 3. **面包屑导航示例**
```
监听文件夹: C:/Users/<USER>/Pictures/Saved Pictures
├── 子文件夹1/          → 客户端显示: /子文件夹1
├── 子文件夹2/          → 客户端显示: /子文件夹2
├── wallhaven-5gvok7.webp → 客户端显示: /wallhaven-5gvok7.webp
└── photo.jpg          → 客户端显示: /photo.jpg
```

## 📱 **客户端集成**

### 🌐 **Web客户端示例**
```javascript
// 获取文件夹内容
const token = "你的访问令牌";
const response = await fetch(`http://127.0.0.1:3333/api/images?path=/&token=${token}`);
const data = await response.json();

// 显示面包屑
const breadcrumb = data.currentPath || "/"; // 显示为根目录

// 构建图片URL
const imageUrl = `http://127.0.0.1:3333/api/image${data.images[0].path}?token=${token}`;
```

### 📋 **API响应格式**
```json
{
  "images": [
    {
      "name": "wallhaven-5gvok7.webp",
      "path": "/wallhaven-5gvok7.webp",
      "size": 1024000,
      "lastModified": "2024-01-01T12:00:00"
    }
  ],
  "folders": [
    {
      "name": "子文件夹",
      "path": "/子文件夹"
    }
  ]
}
```

## 🎉 **功能完成**

### ✅ **实现的功能**
1. **访问令牌加密** - 所有API请求需要有效令牌
2. **路径映射系统** - 客户端看到简化的路径结构
3. **面包屑导航** - 监听文件夹显示为根目录
4. **安全访问控制** - 隐藏服务器真实路径
5. **UI令牌显示** - 设置页面显示和复制令牌

### 🚀 **现在可以安全使用**
- 🔐 **加密访问**：所有请求都需要令牌验证
- 🗂️ **简化路径**：客户端看到的是 `/` 开始的路径
- 🛡️ **安全防护**：隐藏服务器真实文件系统结构
- 📱 **易于集成**：标准的REST API接口

网络共享功能现在既安全又易用！🎉
