{"artifacts": [{"path": "backend/MinSizeRel/backend.dll"}, {"path": "backend/MinSizeRel/backend.lib"}, {"path": "backend/MinSizeRel/backend.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "_qt_internal_find_third_party_dependencies", "target_compile_definitions", "target_include_directories"], "files": ["backend/CMakeLists.txt", "CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6HttpServer/Qt6HttpServerTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6HttpServer/Qt6HttpServerConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapAtomic.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 1, "file": 0, "line": 70, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 78, "parent": 3}, {"command": 2, "file": 0, "line": 58, "parent": 0}, {"command": 5, "file": 1, "line": 9, "parent": 3}, {"file": 4, "parent": 6}, {"command": 5, "file": 4, "line": 218, "parent": 7}, {"file": 3, "parent": 8}, {"command": 4, "file": 3, "line": 55, "parent": 9}, {"file": 2, "parent": 10}, {"command": 3, "file": 2, "line": 61, "parent": 11}, {"command": 4, "file": 3, "line": 43, "parent": 9}, {"file": 9, "parent": 13}, {"command": 7, "file": 9, "line": 45, "parent": 14}, {"command": 6, "file": 8, "line": 137, "parent": 15}, {"command": 5, "file": 7, "line": 76, "parent": 16}, {"file": 6, "parent": 17}, {"command": 4, "file": 6, "line": 55, "parent": 18}, {"file": 5, "parent": 19}, {"command": 3, "file": 5, "line": 61, "parent": 20}, {"command": 6, "file": 8, "line": 137, "parent": 15}, {"command": 5, "file": 7, "line": 76, "parent": 22}, {"file": 11, "parent": 23}, {"command": 4, "file": 11, "line": 58, "parent": 24}, {"file": 10, "parent": 25}, {"command": 3, "file": 10, "line": 61, "parent": 26}, {"command": 6, "file": 8, "line": 137, "parent": 15}, {"command": 5, "file": 7, "line": 76, "parent": 28}, {"file": 13, "parent": 29}, {"command": 4, "file": 13, "line": 55, "parent": 30}, {"file": 12, "parent": 31}, {"command": 3, "file": 12, "line": 61, "parent": 32}, {"command": 5, "file": 0, "line": 9, "parent": 0}, {"file": 4, "parent": 34}, {"command": 5, "file": 4, "line": 218, "parent": 35}, {"file": 15, "parent": 36}, {"command": 4, "file": 15, "line": 55, "parent": 37}, {"file": 14, "parent": 38}, {"command": 3, "file": 14, "line": 61, "parent": 39}, {"command": 6, "file": 8, "line": 137, "parent": 15}, {"command": 5, "file": 7, "line": 76, "parent": 41}, {"file": 17, "parent": 42}, {"command": 4, "file": 17, "line": 57, "parent": 43}, {"file": 16, "parent": 44}, {"command": 3, "file": 16, "line": 61, "parent": 45}, {"command": 4, "file": 17, "line": 45, "parent": 43}, {"file": 19, "parent": 47}, {"command": 8, "file": 19, "line": 35, "parent": 48}, {"command": 6, "file": 8, "line": 36, "parent": 49}, {"command": 5, "file": 7, "line": 76, "parent": 50}, {"file": 18, "parent": 51}, {"command": 2, "file": 18, "line": 48, "parent": 52}, {"command": 4, "file": 11, "line": 46, "parent": 24}, {"file": 22, "parent": 54}, {"command": 7, "file": 22, "line": 45, "parent": 55}, {"command": 6, "file": 8, "line": 137, "parent": 56}, {"command": 5, "file": 7, "line": 76, "parent": 57}, {"file": 21, "parent": 58}, {"command": 4, "file": 21, "line": 55, "parent": 59}, {"file": 20, "parent": 60}, {"command": 3, "file": 20, "line": 61, "parent": 61}, {"command": 9, "file": 0, "line": 37, "parent": 0}, {"command": 10, "file": 0, "line": 52, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -std:c++17 -MD"}], "defines": [{"backtrace": 63, "define": "BACKEND_LIBRARY"}, {"backtrace": 5, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 5, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 63, "define": "QT_DEPRECATED_WARNINGS"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_HTTPSERVER_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 5, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_OPENGL_LIB"}, {"backtrace": 5, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 5, "define": "QT_QMLMETA_LIB"}, {"backtrace": 5, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 5, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 5, "define": "QT_QML_LIB"}, {"backtrace": 5, "define": "QT_QUICK_LIB"}, {"backtrace": 5, "define": "QT_WEBSOCKETS_LIB"}, {"backtrace": 5, "define": "UNICODE"}, {"backtrace": 5, "define": "WIN32"}, {"backtrace": 5, "define": "WIN64"}, {"backtrace": 5, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 5, "define": "_UNICODE"}, {"backtrace": 5, "define": "_WIN64"}, {"define": "backend_EXPORTS"}], "includes": [{"backtrace": 0, "path": "C:/Qt/file/palyer/build/backend/backend_autogen/include_MinSizeRel"}, {"backtrace": 64, "path": "C:/Qt/file/palyer/backend"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtConcurrent"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQuick"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlModels"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtHttpServer"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtWebSockets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "17"}, "sourceIndexes": [0, 7, 8, 9, 10, 11, 12]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "backend::@e17dcb4e28158375c849", "install": {"destinations": [{"backtrace": 2, "path": "bin"}, {"backtrace": 4, "path": "bin"}], "prefix": {"path": "C:/Program Files/palyer"}}, "link": {"commandFragments": [{"fragment": "/machine:x64 /INCREMENTAL:NO", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Concurrent.a", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Quick.a", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6HttpServer.a", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlMeta.a", "role": "libraries"}, {"backtrace": 21, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlWorkerScript.a", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlModels.a", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Qml.a", "role": "libraries"}, {"backtrace": 27, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6OpenGL.a", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 33, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 40, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6WebSockets.a", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 46, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 53, "fragment": "-latomic", "role": "libraries"}, {"backtrace": 62, "fragment": "ws2_32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "backend", "nameOnDisk": "backend.dll", "paths": {"build": "backend", "source": "backend"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 7, 8, 9, 10, 11, 12]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/backend/backend_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "backend/ConversionTaskRunnable.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "backend/ImageConversionManager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "backend/ImageProcessor.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "backend/NetworkBackend.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "backend/server/ClientMode.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "backend/server/ServerMode.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "backend/ConversionTaskRunnable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "backend/ImageConversionManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "backend/ImageProcessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "backend/NetworkBackend.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "backend/server/ClientMode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "backend/server/ServerMode.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}