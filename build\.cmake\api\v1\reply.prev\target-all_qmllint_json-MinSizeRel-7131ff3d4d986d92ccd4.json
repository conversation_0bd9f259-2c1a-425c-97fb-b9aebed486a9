{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_add_phony_target_deferred"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake:287:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 1, "file": 1, "line": 1, "parent": 1}, {"command": 0, "file": 0, "line": 342, "parent": 2}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "/QmlLinter"}, "id": "all_qmllint_json::@6890427a1f51a3e7e1df", "name": "all_qmllint_json", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/CMakeFiles/all_qmllint_json", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/60efe0b784417ad25bcf545a28e72a55/all_qmllint_json.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}