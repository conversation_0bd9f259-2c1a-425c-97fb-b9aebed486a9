import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtCore
import QtQuick.Dialogs
import Qt.labs.folderlistmodel
import QtQuick.Window
import QtQuick.Controls.Basic

Item {
    id: imagePage
    width: parent.width; height: parent.height

    // 属性
    Settings { id: appSettings; category: "ImageBrowser"; property string lastPath: "" }
    property string currentPath: ""; property string fullPath: ""; property string highlightedPath: ""; property string highlightedText: ""
    property var previewWindows: []; property int maxPreviewWindows: 10; property bool mouseInsidePage: false
    property int sortMode: 0; property var sortModeNames: [ "自然排序", "更改时间排序", "创建时间排序" ]

    // 网络模式相关属性 - 只有用户主动连接服务器才启用
    property bool isNetworkMode: false  // 默认本地模式，由用户在设置中连接服务器时设置
    property string networkBaseUrl: ""

    onCurrentPathChanged: {
        var path = currentPath.replace(/^file:\/\/+/, "").replace(/^file:\/\//, "")
        var parts = path.split(/[\/\\]+/).filter(function(p){return p.length>0})
        highlightedText = parts.length > 0 ? parts[parts.length-1] : ""
        if (!fullPath) fullPath = currentPath
    }

    // 复制提示框
    Rectangle {
        id: copyTip
        width: tipText.width + 20; height: 30; radius: 6
        color: Qt.rgba(1, 1, 1, 0.8); visible: opacity > 0; opacity: 0; z: 1000
        Behavior on opacity { NumberAnimation { duration: 200 } }
        Text { id: tipText; text: "路径已复制"; anchors.centerIn: parent; color: "#333"; font.pixelSize: 14 }
        Timer { id: tipTimer; interval: 600; onTriggered: copyTip.opacity = 0 }
    }
    // 文件名提示框
    Rectangle {
        id: fileNameTip
        width: nameText.width + 20; height: nameText.height + 10; radius: 6
        color: Qt.rgba(0.1, 0.1, 0.1, 0.95); visible: opacity > 0; opacity: 0; z: 1000
        border.color: Qt.rgba(0.3, 0.3, 0.3, 0.7); border.width: 1
        Behavior on opacity { NumberAnimation { duration: 200 } }
        Text { id: nameText; anchors.centerIn: parent; color: "#FFF"; font.pixelSize: 14; font.bold: true }
        property string text: ""
        onTextChanged: {
            nameText.text = text
            x = Math.min(x, imagePage.width - width - 5); y = Math.min(y, imagePage.height - height - 5)
        }
    }
    // 排序通知条
    Rectangle {
        id: sortTip
        width: sortTipText.width + 40; height: 36; radius: 8; color: Qt.rgba(0,0,0,0.8); opacity: 0; z: 9999
        anchors { horizontalCenter: parent.horizontalCenter; bottom: parent.bottom; bottomMargin: 80 }
        Behavior on opacity { NumberAnimation { duration: 200 } }
        Text { id: sortTipText; anchors.centerIn: parent; color: "#0F9D58"; font.pixelSize: 16; font.bold: true }
        Timer { id: sortTipTimer; interval: 1200; onTriggered: sortTip.opacity = 0 }
    }


    // 主布局
    ColumnLayout {
        anchors.fill: parent; spacing: 0
        // 顶部路径栏
        Rectangle {
            height: 40; Layout.fillWidth: true; color: "transparent"
            ToolButton {
                text: "📁"; width: 30; height: 30
                anchors { left: parent.left; leftMargin: 5; verticalCenter: parent.verticalCenter }
                background: Rectangle { color: "transparent" }
                onClicked: fileDialog.open()
            }
            // 面包屑导航
            Item {
                anchors { left: parent.left; leftMargin: 40; right: parent.right; rightMargin: 10; verticalCenter: parent.verticalCenter }
                height: parent.height; clip: true
                Row {
                    anchors.verticalCenter: parent.verticalCenter
                    Repeater {
                        id: breadcrumbRepeater
                        model: getBreadcrumbParts(fullPath)
                        Row {
                            Text {
                                text: modelData.name; font.pixelSize: 14
                                font.bold: modelData.name === highlightedText; color: "#F5F5F5"
                                Component.onCompleted: { if (index === breadcrumbRepeater.count - 1 && !highlightedPath) highlightedPath = modelData.path }
                                MouseArea {
                                    anchors.fill: parent; cursorShape: Qt.PointingHandCursor; acceptedButtons: Qt.LeftButton | Qt.RightButton
                                    onClicked: function(mouse) {
                                        if (mouse.button === Qt.LeftButton) {
                                            highlightedPath = modelData.path
                                            if (currentPath !== modelData.path) {
                                                currentPath = modelData.path; loadImagesFromFolder()
                                            }
                                        } else if (mouse.button === Qt.RightButton) {
                                            _copyPathToClipboard(
                                                modelData.path.replace(/^file:\/\/+/, "").replace(/\//g, "\\"),
                                                mapToItem(imagePage, mouse.x, mouse.y)
                                            )
                                        }
                                    }
                                }
                            }
                            Text { text: " ➔ "; font.pixelSize: 14; font.bold: true; color: "#FFF"; visible: index < (breadcrumbRepeater.count - 1) }
                        }
                    }
                }
            }
        }
        Rectangle { height: 1; Layout.fillWidth: true; color: Qt.rgba(0.7, 0.7, 0.7, 0.2) }
        // 图片网格视图
        Rectangle {
            Layout.fillWidth: true; Layout.fillHeight: true; color: Qt.rgba(16/255, 18/255, 27/255, 0.4)
            Flickable {
                id: imageFlickable
                anchors.fill: parent
                topMargin: 20; bottomMargin: 15; leftMargin: 20; rightMargin: 25; clip: true
                contentWidth: imageGrid.width; contentHeight: imageGrid.height
                property bool isScrolling: moving || flicking
                Timer { id: scrollHideTimer; interval: 1000; onTriggered: { if (!imageFlickable.isScrolling && !vbarArea.containsMouse) vbar.opacity = 0 } }
                onIsScrollingChanged: { if (isScrolling) vbar.opacity = 0.8; else scrollHideTimer.restart() }
                onContentYChanged: requestVisibleThumbnails(); onHeightChanged: requestVisibleThumbnails(); onWidthChanged: requestVisibleThumbnails()
                ScrollBar.vertical: ScrollBar {
                    id: vbar; policy: ScrollBar.AsNeeded
                    visible: imageFlickable.contentHeight > imageFlickable.height
                    width: 6; padding: 0; opacity: 0
                    Behavior on opacity { NumberAnimation { duration: 300 } }
                    background: Rectangle {
                        color: "transparent"
                        MouseArea {
                            id: vbarArea; anchors { fill: parent; leftMargin: -5; rightMargin: -5 } hoverEnabled: true
                            onEntered: vbar.opacity = 0.8; onExited: { if (!imageFlickable.isScrolling) scrollHideTimer.restart() }
                        }
                    }
                    contentItem: Rectangle { implicitWidth: 6; radius: width / 2; color: parent.pressed ? Qt.rgba(0.9, 0.9, 0.9, 0.9) : Qt.rgba(0.8, 0.8, 0.8, 0.7) }
                }
                GridLayout {
                    id: imageGrid
                    width: imageFlickable.width - imageFlickable.leftMargin - imageFlickable.rightMargin
                    property int targetItemWidth: 160
                    columns: Math.max(1, Math.floor(width / targetItemWidth))
                    columnSpacing: 10; rowSpacing: 10
                    Repeater { model: imageProcessor
                        // 图片单元格
                        Rectangle {
                            id: imageCell
                            property string currentSource: imageProcessor.getThumbnailSource(index)
                            Layout.fillWidth: true
                            Layout.preferredWidth: (imageGrid.width - imageGrid.columnSpacing * (imageGrid.columns - 1)) / imageGrid.columns
                            Layout.preferredHeight: Layout.preferredWidth
                            color: Qt.rgba(1, 1, 1, thumbnailMouseArea.containsMouse ? 0.25 : 0.05); radius: 6
                            Behavior on color { ColorAnimation { duration: 300 } }
                            Item {
                                anchors.fill: parent; anchors.margins: 5; property int fileNameHeight: 30
                                // 内容区域
                                Item {
                                    id: imageContainer
                                    anchors { top: parent.top; left: parent.left; right: parent.right }
                                    height: parent.height - parent.fileNameHeight - 5; z: 3
                                    opacity: model.isFolder || (currentSource && currentSource.length > 0) || thumbnail.everReady ? 1.0 : 0.0
                                    Behavior on opacity { NumberAnimation { duration: 300 } }
                                    // 文件夹图标
                                    Text {
                                        id: folderIcon
                                        visible: model.isFolder === true; text: "📁"; anchors.centerIn: parent
                                        font.pixelSize: parent.height * 0.6; scale: 1.0
                                        
                                        // 文件夹名称悬停提示
                                        MouseArea {
                                            anchors.fill: parent; hoverEnabled: true; propagateComposedEvents: true
                                            
                                            onEntered: {
                                                fileNameTip.text = model.fileName
                                                var mappedPos = mapToItem(imagePage, mouseX, mouseY)
                                                // 将提示框显示在鼠标右下方
                                                fileNameTip.x = Math.min(mappedPos.x + 15, imagePage.width - fileNameTip.width - 10)
                                                fileNameTip.y = Math.min(mappedPos.y + 15, imagePage.height - fileNameTip.height - 10)
                                                fileNameTip.opacity = 0.9
                                            }
                                            
                                            onExited: {
                                                fileNameTip.opacity = 0
                                            }
                                            
                                            onPositionChanged: {
                                                if (fileNameTip.opacity > 0) {
                                                    var mappedPos = mapToItem(imagePage, mouseX, mouseY)
                                                    fileNameTip.x = Math.min(mappedPos.x + 15, imagePage.width - fileNameTip.width - 10)
                                                    fileNameTip.y = Math.min(mappedPos.y + 15, imagePage.height - fileNameTip.height - 10)
                                                }
                                            }
                                            
                                            onClicked: function(mouse) {
                                                mouse.accepted = false // 确保点击事件传递给父级
                                            }
                                        }
                                    }
                                    // 图片
                                    Image {
                                        id: thumbnail
                                        property bool everReady: false
                                        anchors.fill: parent; anchors.margins: 5
                                        fillMode: Image.PreserveAspectFit; asynchronous: true; cache: true
                                        source: imageCell.currentSource // 直接使用返回的URL
                                        visible: !model.isFolder
                                        scale: thumbnailMouseArea.containsMouse ? 1.1 : 1.0
                                        Behavior on scale { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                                        // 处理加载错误
                                        onStatusChanged: {
                                            if (status === Image.Error) {
                                                errorPlaceholder.visible = true;
                                            } else if (status === Image.Ready) {
                                                errorPlaceholder.visible = false;
                                                everReady = true;
                                            }
                                        }
                                        BusyIndicator {
                                            anchors.centerIn: parent
                                            running: imageProcessor.isItemLoading(index) && !thumbnail.everReady
                                            visible: running
                                        }
                                    }
                                    // 错误占位图
                                    Rectangle {
                                        id: errorPlaceholder
                                        anchors.fill: parent
                                        color: "transparent"
                                        visible: false
                                        Text {
                                            anchors.centerIn: parent
                                            text: "⚠️"
                                            font.pixelSize: 24
                                            color: "#FF5555"
                                        }
                                    }
                                    // 鼠标交互
                                    MouseArea {
                                        id: thumbnailMouseArea
                                        anchors.fill: parent; hoverEnabled: true; z: 100
                                        onDoubleClicked: {
                                            if (model.isFolder) {
                                                currentPath = model.filePath
                                                fullPath = model.filePath
                                                highlightedPath = model.filePath
                                                // 重置滚动位置
                                                imageFlickable.contentY = 0
                                                loadImagesFromFolder()
                                            } else if (model.filePath) {
                                                openImagePreview(index)
                                            }
                                        }
                                    }
                                }
                                // 文件名
                                Rectangle {
                                    id: fileNameRect
                                    anchors { left: parent.left; right: parent.right; bottom: parent.bottom }
                                    height: parent.fileNameHeight; color: Qt.rgba(0, 0, 0, 0.6); radius: 4; z: 4
                                    Text {
                                        id: fileNameTextEdit
                                        anchors.fill: parent; anchors.margins: 5
                                        text: model.fileName; color: "white"; font.pixelSize: 12
                                        font.bold: true
                                        verticalAlignment: Text.AlignVCenter
                                        horizontalAlignment: textMetrics.width > width - 10 ? Text.AlignLeft : Text.AlignHCenter
                                        textFormat: Text.PlainText
                                        wrapMode: Text.NoWrap
                                        clip: true
                                        // 使用TextMetrics计算文本宽度
                                        TextMetrics {
                                            id: textMetrics
                                            font: fileNameTextEdit.font
                                            text: fileNameTextEdit.text
                                        }
                                        // 鼠标悬停处理
                                        MouseArea {
                                            id: fileNameMouseArea
                                            anchors.fill: parent
                                            acceptedButtons: Qt.RightButton | Qt.LeftButton
                                            propagateComposedEvents: true
                                            hoverEnabled: true
                                            onEntered: {
                                                // 始终显示完整文件名提示，不论是否超出
                                                fileNameTip.text = model.fileName
                                                var mappedPos = mapToItem(imagePage, mouseX, mouseY)
                                                // 将提示框显示在鼠标右下方
                                                fileNameTip.x = Math.min(mappedPos.x + 15, imagePage.width - fileNameTip.width - 10)
                                                fileNameTip.y = Math.min(mappedPos.y + 15, imagePage.height - fileNameTip.height - 10)
                                                fileNameTip.opacity = 0.9
                                            }
                                            onExited: {
                                                fileNameTip.opacity = 0
                                            }
                                            onPositionChanged: {
                                                if (fileNameTip.opacity > 0) {
                                                    var mappedPos = mapToItem(imagePage, mouseX, mouseY)
                                                    fileNameTip.x = Math.min(mappedPos.x + 15, imagePage.width - fileNameTip.width - 10)
                                                    fileNameTip.y = Math.min(mappedPos.y + 15, imagePage.height - fileNameTip.height - 10)
                                                }
                                            }
                                            
                                            onClicked: function(mouse) {
                                                mouse.accepted = false // 确保点击事件传递给父级
                                            }
                                        }
                                    }
                                }
                            }
                            Connections {
                                target: imageProcessor
                                function onThumbnailReady(readyIndex) {
                                    if (readyIndex === index) {
                                        // 尝试获取缩略图
                                        var thumbUrl = imageProcessor.getThumbnailImage(index);
                                        if (thumbUrl && thumbUrl.length > 0) {
                                            imageCell.currentSource = thumbUrl;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            BusyIndicator { anchors.centerIn: parent; running: imageProcessor.processing; visible: running }
        }
    }
    // 底部工具条
    Rectangle {
        id: bottomToolbar; z: 10
        width: parent.width * 0.7; height: 40; radius: 10
        color: Qt.rgba(26/255, 28/255, 39/255, 0.8); opacity: 0; visible: opacity > 0
        anchors { horizontalCenter: parent.horizontalCenter; bottom: parent.bottom; bottomMargin: parent.height * 0.2 }
        Behavior on opacity { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
        Row {
            spacing: 20; padding: 10
            anchors { verticalCenter: parent.verticalCenter; left: parent.left }
            // 缩放按钮
            Item {
                width: 30; height: 30
                Text { text: "🔄"; font.pixelSize: 20; color: "#FFFFFF"; anchors.centerIn: parent }
                Rectangle {
                    anchors.fill: parent; color: Qt.rgba(1.0, 1.0, 1.0, 0.2); radius: 5; opacity: 0
                    Behavior on opacity { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                }
                MouseArea {
                    hoverEnabled: true; anchors.fill: parent
                    onEntered: {
                        parent.children[1].opacity = 0.6
                        toolTip.x = mapToItem(imagePage, mouseX, mouseY).x - toolTip.width/2 + 50
                        toolTip.y = mapToItem(imagePage, mouseX, mouseY).y - toolTip.height + 35
                        toolTip.opacity = 0.9; toolTipText.text = "转换为Webp"
                    }
                    onExited: { parent.children[1].opacity = 0; toolTip.opacity = 0 }
                    onPositionChanged: {
                        if (toolTip.opacity > 0) {
                            toolTip.x = mapToItem(imagePage, mouseX, mouseY).x - toolTip.width/2 + 60
                            toolTip.y = mapToItem(imagePage, mouseX, mouseY).y - toolTip.height + 45
                        }
                    }
                    onClicked: { openConversionWindowWithFormat(currentPath, "webp") }
                }
            }
            // AVIF转换按钮
            Item {
                width: 30; height: 30
                Text {
                    text: "🅰️"
                    font.pixelSize: 20
                    color: "#FFFFFF"
                    anchors.centerIn: parent
                }
                Rectangle {
                    anchors.fill: parent;
                    color: Qt.rgba(1.0, 1.0, 1.0, 0.2)
                    radius: 5;
                    opacity: 0
                    Behavior on opacity { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                }
                MouseArea {
                    hoverEnabled: true; anchors.fill: parent
                    onEntered: {
                        parent.children[1].opacity = 0.6
                        toolTip.x = mapToItem(imagePage, mouseX, mouseY).x - toolTip.width/2 + 50
                        toolTip.y = mapToItem(imagePage, mouseX, mouseY).y - toolTip.height + 35
                        toolTip.opacity = 0.9
                        toolTipText.text = "转换为AVIF"
                    }
                    onExited: { parent.children[1].opacity = 0; toolTip.opacity = 0 }
                    onPositionChanged: {
                        if (toolTip.opacity > 0) {
                            toolTip.x = mapToItem(imagePage, mouseX, mouseY).x - toolTip.width/2 + 60
                            toolTip.y = mapToItem(imagePage, mouseX, mouseY).y - toolTip.height + 45
                        }
                    }
                    onClicked: { openConversionWindowWithFormat(currentPath, "avif") }
                }
            }
        }
    }
    // 底部工具条提示框
    Rectangle {
        id: toolTip
        width: toolTipText.width + 20; height: toolTipText.height + 10; radius: 6
        color: Qt.rgba(0.1, 0.1, 0.1, 0.9); visible: opacity > 0; opacity: 0; z: 1000
        Behavior on opacity { NumberAnimation { duration: 200; easing.type: Easing.InOutQuad } }
        Text {
            id: toolTipText
            text: "转换为Webp"
            anchors.centerIn: parent
            color: "#FFFFFF"
            font.pixelSize: 13
            wrapMode: Text.NoWrap
        }
    }
    // 辅助组件
    TextEdit { id: textEdit; visible: false }

    // 唤出工具栏
    Shortcut{
        sequence: " "; enabled: mouseInsidePage
        onActivated: { bottomToolbar.opacity = !bottomToolbar.opacity }
    }

    Shortcut {
        sequence: "q"
        onActivated: {
            sortMode = (sortMode + 1) % 3
            imageProcessor.sortMode = [0,2,6][sortMode]
            showSortTip(sortModeNames[sortMode])
        }
    }

    // 对话框
    FolderDialog {
        id: fileDialog
        title: "选择文件夹"
        currentFolder: StandardPaths.standardLocations(StandardPaths.PicturesLocation)[0]
        onAccepted: {
            currentPath = selectedFolder; fullPath = selectedFolder
            appSettings.lastPath = currentPath; highlightedPath = currentPath
            loadImagesFromFolder()
        }
    }

    Dialog {
        id: maxWindowsDialog
        title: "提示"; modal: true; standardButtons: Dialog.Ok; anchors.centerIn: parent; width: 300
        Text {
            width: parent.width; wrapMode: Text.WordWrap
            text: "已达到最大预览窗口数量 (" + maxPreviewWindows + ")，请先关闭部分窗口。"
            color: "#EEEEEE"
        }
    }

    // --------------------- 信号处理器和初始化逻辑 ---------------------
    MouseArea{
        anchors.fill: parent
        hoverEnabled: true;z: -1
        onEntered: {mouseInsidePage = true}
        onExited: {mouseInsidePage = false}
    }

    Component.onCompleted: {
        if (appSettings.lastPath) {
            currentPath = appSettings.lastPath;
            fullPath = appSettings.lastPath;
            loadImagesFromFolder();
        }
        imagePage.forceActiveFocus();
    }

    // --------------------- JavaScript 函数定义 ---------------------

    function getBreadcrumbParts(path) {
        if (!path) return [];
        let displayPath = path.toString().replace(/^file:\/\/+/, "").replace(/^file:\/\//, "");
        let parts = displayPath.split(/[\/\\]+/).filter(part => part.length > 0);
        let result = [];
        let currentPathSoFar = "file:///";
        if (parts.length > 0 && parts[0].includes(":")) {
            result.push({ name: parts[0], path: currentPathSoFar + parts[0] });
            currentPathSoFar += parts[0] + "/";
            parts = parts.slice(1);
        }
        parts.forEach((part, i) => {
            currentPathSoFar += part + (i < parts.length - 1 ? "/" : "");
            result.push({ name: part, path: currentPathSoFar });
        });
        // 确保最后一个路径被高亮显示
        if (result.length > 0) {
            Qt.callLater(function() {
                if (highlightedPath !== result[result.length-1].path) highlightedPath = result[result.length-1].path;
            });
        }
        return result;
    }

    function loadImagesFromFolder() {
        if (!currentPath) return
        let path = currentPath.toString().replace(/^file:\/\/+/, "")
        if (path.startsWith("/")) path = path.substring(1)
        appSettings.lastPath = path; imageProcessor.loadImagesFromFolder(path, true)
        Qt.callLater(function() {
            imageFlickable.contentY = -imageFlickable.topMargin; requestVisibleThumbnails()
        })
    }

    function openImagePreview(index) {
        if (previewWindows.length >= maxPreviewWindows) { maxWindowsDialog.open(); return; }
        var component = Qt.createComponent("qrc:/compoment/ImagePreviewWindow.qml");
        if (component.status === Component.Ready) {
            var window = component.createObject(imagePage, {
                "imageIndex": index,
                "windowId": previewWindows.length
            });
            window.windowClosed.connect(function(id) { removePreviewWindow(id) });
            window.x = Screen.width / 2 - window.width / 2 + previewWindows.length * 30;
            window.y = Screen.height / 2 - window.height / 2 + previewWindows.length * 30;
            previewWindows.push(window);
            window.show();
            window.requestActivate();
        } else {
            console.error("Failed to create ImagePreviewWindow component:", component.errorString());
        }
    }

    function removePreviewWindow(windowId) {
        for (let i = 0; i < previewWindows.length; i++) {
            if (previewWindows[i].windowId === windowId) {
                let window = previewWindows[i];
                previewWindows.splice(i, 1);
                Qt.callLater(function() { if (window) window.destroy(); });
                break;
            }
        }
    }

    function showFileNameTip(name, pos) {
        fileNameTip.text = name; fileNameTip.x = pos.x + 15; fileNameTip.y = pos.y + 15
        if (fileNameTip.x + fileNameTip.width > imagePage.width) fileNameTip.x = imagePage.width - fileNameTip.width - 5
        if (fileNameTip.y + fileNameTip.height > imagePage.height) fileNameTip.y = imagePage.height - fileNameTip.height - 5
        fileNameTip.opacity = 1
    }

    function showMaxWindowsMessage() { maxWindowsDialog.open() }

    function _copyPathToClipboard(path, position) {
        textEdit.text = path; textEdit.selectAll(); textEdit.copy()
        copyTip.x = Math.min(position.x + 10, imagePage.width - copyTip.width - 10)
        copyTip.y = Math.min(position.y + 20, imagePage.height - copyTip.height - 10)
        copyTip.opacity = 1; tipTimer.restart()
    }

    function showCopyTip(message) {
        tipText.text = message || "已复制"
        copyTip.x = Math.min(imageFlickable.width / 2, imagePage.width - copyTip.width - 10)
        copyTip.y = Math.min(imageFlickable.height / 2, imagePage.height - copyTip.height - 10)
        copyTip.opacity = 1; tipTimer.restart()
    }

    function openConversionWindowWithFormat(folderPath, format) {
        // 确保folderPath是有效的
        if (!folderPath) {
            folderPath = currentPath || "";
        }
        
        // 对路径进行规范化处理
        let normalizedPath = folderPath.toString();
        
        var component = Qt.createComponent("qrc:/compoment/toolbar/ConversionWindow.qml");
        if (component.status === Component.Ready) {
            var window = component.createObject(imagePage, {
                "windowId": Math.floor(Math.random() * 1000),
                "currentFolder": normalizedPath,
                "targetFormat": format || "webp",
                "includeSubfolders": true
            });
            window.visible = true;
            window.requestActivate();
        } else {
            console.error("组件创建失败:", component.errorString());
        }
    }

    function requestVisibleThumbnails() {
        var rowHeight = imageGrid.columns > 0 ? imageGrid.children[0].implicitHeight : 160;
        var columns = imageGrid.columns;
        var totalCount = imageProcessor.count;
        if (columns <= 0 || totalCount <= 0) return;
        var visibleRows = Math.ceil(imageFlickable.height / rowHeight);
        var firstRow = Math.floor(imageFlickable.contentY / rowHeight);
        var preloadRows = 5;
        var startIndex = Math.max(0, (firstRow - preloadRows) * columns);
        var endIndex = Math.min(totalCount - 1, (firstRow + visibleRows + preloadRows) * columns - 1);
        imageProcessor.requestThumbnails(startIndex, endIndex);
    }

    function showSortTip(msg) {
        sortTipText.text = "排序方式：" + msg; sortTip.opacity = 1; sortTipTimer.restart()
    }
}
