import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Dialogs
import QtQuick.Controls.Basic

Window {
    id: root
    width: 800; height: 600; visible: true
    flags: Qt.FramelessWindowHint | Qt.Window; color:"transparent"; opacity: 0
    signal openFileDialog(); signal defaultback(); signal closingset()
    Behavior on opacity { NumberAnimation { duration: 300; easing.type: Easing.InOutQuad } }
    Rectangle {
        anchors.fill: parent; radius: 10
        gradient: Gradient {
            GradientStop { position: 0.0; color: "#DFE3EA" }
            GradientStop { position: 1.0; color: "#C5CDD9" }
        }
        Item {
            id: titleBar
            anchors { left: parent.left; top: parent.top; right: parent.right }
            height: 40

            MouseArea {
                anchors.fill: parent
                onPressed: root.startSystemMove()
            }

            Row {
                anchors.left: parent.left
                anchors.leftMargin: 13
                anchors.verticalCenter: parent.verticalCenter
                spacing: 8

                Rectangle {
                    id: closeBtn
                    width: 13; height: 13; radius: width/2; color: "#ff5f56"; opacity: 0.9
                    Behavior on scale { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                    Behavior on opacity { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                    Text {
                        text: "×"; color: "black"; opacity: 0.8; anchors.centerIn: parent
                        transform: Translate {x: -0.2; y: -0.2} visible: closeArea.containsMouse
                        font { pixelSize: 10; bold: true }
                    }
                    MouseArea {
                        id: closeArea; hoverEnabled: true; anchors.fill: parent
                        onClicked: { root.opacity = 0; closeTimer.start() }
                        onEntered: {closeBtn.scale = 1.5; closeBtn.opacity = 1}
                        onExited: {closeBtn.scale = 1; closeBtn.opacity = 0.9}
                    }
                    Timer {
                        id: closeTimer; interval: 300; repeat: false
                        onTriggered: closingset()
                    }
                }

                Rectangle {
                    id: minimizeBtn
                    width: 13; height: 13; radius: width/2; color: "#ffbd2e"; opacity: 0.9
                    Behavior on scale { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                    Behavior on opacity { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                    Text {
                        text: "-"; color: "black"; opacity: 0.8; anchors.centerIn: parent
                        transform: Translate {x: -0.6; y: -0.6}
                        visible: minimizeArea.containsMouse
                        font { pixelSize: 10; bold: true }
                    }
                    MouseArea {
                        id:minimizeArea; anchors.fill: parent; hoverEnabled: true
                        onClicked: {}
                        onEntered: {minimizeBtn.scale = 1.5; minimizeBtn.opacity = 1}
                        onExited: {minimizeBtn.scale = 1; minimizeBtn.opacity = 0.9}
                    }
                }

                Rectangle {
                    id: fullscreenBtn
                    width: 13; height: 13; radius: width/2; color: "#27c93f"; opacity: 0.9
                    Behavior on scale { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                    Behavior on opacity { NumberAnimation { duration: 300; easing.type: Easing.OutQuad } }
                    MouseArea {
                        id: fullscreenArea; hoverEnabled: true; anchors.fill: parent
                        onEntered: {fullscreenBtn.scale = 1.5; fullscreenBtn.opacity = 1}
                        onExited: {fullscreenBtn.scale = 1; fullscreenBtn.opacity = 0.9}
                    }
                }
            }
        }

        // 主体内容区域
        Item {
            anchors { top: titleBar.bottom; left: parent.left; right: parent.right; bottom: parent.bottom; margins: 20; topMargin: 10 }

            // 左侧导航栏
            Rectangle {
                id: leftSidebar
                width: 200; height: parent.height; color: "#F5F7FA"; radius: 8

                Column {
                    anchors { fill: parent; margins: 15 }
                    spacing: 8

                    // 搜索框
                    Rectangle {
                        width: parent.width; height: 35; color: "white"; radius: 6
                        border { color: "#E1E5E9"; width: 1 }
                        Row {
                            anchors.centerIn: parent; spacing: 8
                            Text { text: "🔍"; font.pixelSize: 14; color: "#8B95A1" }
                            Text { text: "搜索"; font.pixelSize: 13; color: "#8B95A1" }
                        }
                    }

                    // 菜单项列表
                    ListView {
                        width: parent.width; height: parent.height - 50
                        model: menuModel; delegate: menuDelegate; spacing: 2; clip: true

                        Component {
                            id: menuDelegate
                            Rectangle {
                                width: ListView.view.width; height: 40; radius: 6
                                color: model.selected ? "#E3F2FD" : (hovered ? "#F0F4F8" : "transparent")
                                property bool hovered: false

                                MouseArea {
                                    anchors.fill: parent; hoverEnabled: true
                                    onClicked: {
                                        for(var i = 0; i < menuModel.count; i++)
                                            menuModel.setProperty(i, "selected", false)
                                        menuModel.setProperty(index, "selected", true)
                                        parent.ListView.view.currentIndex = index
                                    }
                                    onEntered: parent.hovered = true
                                    onExited: parent.hovered = false
                                }

                                Row {
                                    anchors { left: parent.left; leftMargin: 12; verticalCenter: parent.verticalCenter }
                                    spacing: 10
                                    Text {
                                        text: model.icon; font.pixelSize: 16
                                        color: model.selected ? "#1976D2" : "#5F6368"
                                    }
                                    Text {
                                        text: model.title; font.pixelSize: 13
                                        color: model.selected ? "#1976D2" : "#333333"
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 右侧内容区域
            Rectangle {
                id: rightContent
                anchors { left: leftSidebar.right; right: parent.right; top: parent.top; bottom: parent.bottom; leftMargin: 20 }
                color: "white"; radius: 8; clip: true

                // 动态内容加载器
                Flickable {
                    id: rightFlick
                    anchors.fill: parent
                    contentWidth: width
                    contentHeight: contentColumn.implicitHeight
                    clip: true

                    Column {
                        id: contentColumn
                        width: parent.width; spacing: 0
                        anchors { left: parent.left; right: parent.right; top: parent.top; margins: 0 }

                        // 顶部留白
                        Rectangle { width: 1; height: 20; color: "transparent" }

                        // 左右留白
                        Row {
                            width: parent.width; spacing: 0
                            Rectangle { width: 20; height: 1; color: "transparent" }

                            Loader {
                                id: contentLoader
                                width: parent.width - 40
                                sourceComponent: {
                                    var selectedItem = null
                                    for(var i = 0; i < menuModel.count; i++) {
                                        if(menuModel.get(i).selected) {
                                            selectedItem = menuModel.get(i)
                                            break
                                        }
                                    }
                                    if(selectedItem) {
                                        if(selectedItem.component === "ServerSettings") {
                                            return serverSettingsComponent
                                        } else if(selectedItem.component === "ConnectionSettings") {
                                            return connectionSettingsComponent
                                        }
                                    }
                                    return serverSettingsComponent
                                }
                            }

                            Rectangle { width: 20; height: 1; color: "transparent" }
                        }

                        // 底部留白
                        Rectangle { width: 1; height: 20; color: "transparent" }
                    }
                    ScrollBar.vertical: ScrollBar {
                        id: flickScrollBar
                        policy: ScrollBar.AsNeeded; width: 6; padding: 0
                        visible: rightFlick.contentHeight > rightFlick.height || active || pressed || hovered || rightFlick.moving
                        opacity: active || pressed || hovered || rightFlick.moving ? 0.8 : 0
                        Behavior on opacity { NumberAnimation { duration: 500; easing.type: Easing.OutCubic } }
                        contentItem: Rectangle {
                            implicitWidth: 6; radius: width / 2
                            color: parent.pressed ? Qt.rgba(0.9, 0.9, 0.9, 0.95) : Qt.rgba(0.8, 0.8, 0.8, 0.95)
                        }
                    }
                }

                // 服务器设置组件
                Component {
                    id: serverSettingsComponent

                    Column {
                        spacing: 20
                        width: parent.width;

                        // 当前IP地址卡片
                        Rectangle {
                            width: parent.width
                            implicitHeight: ipColumn.implicitHeight + 32
                            color: "#F8F9FA";radius: 8
                            border.color: "#E9ECEF";border.width: 1
                            Column {
                                id: ipColumn
                                anchors.left: parent.left
                                anchors.leftMargin: 20
                                anchors.right: parent.right
                                anchors.rightMargin: 20
                                anchors.top: parent.top
                                anchors.topMargin: 16
                                anchors.bottom: parent.bottom
                                anchors.bottomMargin: 16
                                spacing: 12
                                Text {text: "当前IP地址"; font { pixelSize: 16; bold: true }color: "#495057"}
                                Row {
                                    spacing: 12
                                    height: 28
                                    Text {
                                        text: "IPv4:";font.pixelSize: 13;font.bold: true
                                        color: "#6C757D";height: parent.height;verticalAlignment: Text.AlignVCenter
                                    }
                                    Repeater {
                                        model: networkBackend ? networkBackend.ipv4Addresses : []
                                        delegate: Rectangle {
                                            color: "#E3F2FD"
                                            radius: 5
                                            height: 28
                                            implicitWidth: ipText.implicitWidth + 20
                                            TextInput {
                                                id: ipText;readOnly: true
                                                anchors.centerIn: parent
                                                text: modelData
                                                font.pixelSize: 13
                                                font.family: "Consolas, Monaco, monospace"
                                                color: "#212529"
                                            }
                                        }
                                    }
                                }
                                Row {
                                    spacing: 12
                                    height: 28
                                    Text {
                                        text: "IPv6:";font.pixelSize: 13;font.bold: true
                                        color: "#6C757D";height: parent.height;verticalAlignment: Text.AlignVCenter
                                    }
                                    Repeater {
                                        model: networkBackend ? networkBackend.ipv6Addresses : []
                                        delegate: Rectangle {
                                            color: "#E8F5E9"
                                            radius: 5
                                            height: 28
                                            implicitWidth: ip6Text.implicitWidth + 20
                                            TextInput {
                                                id: ip6Text
                                                anchors.centerIn: parent
                                                text: modelData;readOnly: true
                                                font.pixelSize: 13
                                                font.family: "Consolas, Monaco, monospace"
                                                color: "#212529"
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 服务端口设置卡片
                        Rectangle {
                            width: parent.width
                            implicitHeight: portColumn.implicitHeight + 32
                            color: "#F8F9FA"
                            radius: 8
                            border.color: "#E9ECEF"
                            border.width: 1
                            Column {
                                id: portColumn
                                anchors.left: parent.left
                                anchors.leftMargin: 20
                                anchors.right: parent.right
                                anchors.rightMargin: 20
                                anchors.top: parent.top
                                anchors.topMargin: 16
                                anchors.bottom: parent.bottom
                                anchors.bottomMargin: 16
                                spacing: 8
                                Row {
                                    spacing: 10
                                    Text {
                                        text: "服务监听端口"; font { pixelSize: 16; bold: true }
                                        color: "#495057"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    Rectangle {
                                        width: 60; height: 24; color: "#28A745"; radius: 4
                                        anchors.verticalCenter: parent.verticalCenter
                                        Text {
                                            anchors.centerIn: parent
                                            text: "应用"; color: "white"
                                            font { pixelSize: 11; bold: true }
                                        }
                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (typeof networkBackend !== 'undefined') {
                                                    let port = parseInt(portInput.text)
                                                    if (port >= 1024 && port <= 65535) {
                                                        if (networkBackend.serverRunning) {
                                                            networkBackend.stopImageServer()
                                                        }
                                                        networkBackend.startImageServer(port)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    Rectangle {
                                        width: 60; height: 24; color: "#DC3545"; radius: 4
                                        anchors.verticalCenter: parent.verticalCenter
                                        Text {
                                            anchors.centerIn: parent
                                            text: "停止"; color: "white"
                                            font { pixelSize: 11; bold: true }
                                        }
                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (typeof networkBackend !== 'undefined') {
                                                    networkBackend.stopImageServer()
                                                }
                                            }
                                        }
                                    }
                                }

                                // 服务器状态显示
                                Row {
                                    spacing: 15
                                    Text {
                                        text: "服务状态:"; font { pixelSize: 13; bold: true }
                                        color: "#6C757D"; anchors.verticalCenter: parent.verticalCenter
                                    }
                                    Rectangle {
                                        width: 12; height: 12; radius: 6
                                        color: (typeof networkBackend !== 'undefined' && networkBackend.serverRunning) ? "#28A745" : "#DC3545"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    Text {
                                        text: (typeof networkBackend !== 'undefined' && networkBackend.serverRunning) ?
                                              "运行中 (端口: " + networkBackend.serverPort + ")" : "已停止"
                                        font { pixelSize: 13 }
                                        color: (typeof networkBackend !== 'undefined' && networkBackend.serverRunning) ? "#28A745" : "#DC3545"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }

                                Row {
                                    spacing: 15
                                    Text {
                                        text: "端口:"; font { pixelSize: 13; bold: true }
                                        color: "#6C757D"; anchors.verticalCenter: parent.verticalCenter
                                    }
                                    Rectangle {
                                        width: 100; height: 30; color: "white"; radius: 6
                                        border { color: "#CED4DA"; width: 1 }
                                        TextInput {
                                            id: portInput
                                            anchors.fill: parent; anchors.margins: 8
                                            text: "3333"; font { pixelSize: 13; family: "Consolas, Monaco, monospace" }
                                            color: "#212529"; selectByMouse: true
                                            validator: IntValidator { bottom: 1; top: 65535 }
                                            horizontalAlignment: Text.AlignHCenter; verticalAlignment: Text.AlignVCenter
                                        }
                                    }
                                }
                            }
                        }

                        // 监听文件夹设置卡片
                        Rectangle {
                            width: parent.width
                            height: folderColumn.implicitHeight + 40
                            color: "#F8F9FA"
                            radius: 8
                            border.color: "#E9ECEF"
                            border.width: 1

                            Column {
                                id: folderColumn
                                anchors.left: parent.left
                                anchors.leftMargin: 20
                                anchors.top: parent.top
                                anchors.topMargin: 20
                                anchors.right: parent.right
                                anchors.rightMargin: 20
                                spacing: 12

                                Row {
                                    width: parent.width
                                    spacing: 10

                                    Text {
                                        text: "监听文件夹"; font { pixelSize: 16; bold: true }
                                        color: "#495057"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }

                                    Rectangle {
                                        width: 80; height: 28; color: "#007BFF"; radius: 4
                                        anchors.verticalCenter: parent.verticalCenter

                                        Text {
                                            anchors.centerIn: parent
                                            text: "+ 添加"; color: "white"
                                            font { pixelSize: 12; bold: true }
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                folderDialog.open()
                                            }
                                        }
                                    }
                                }

                                ListView {
                                    id: folderListView
                                    width: parent.width; height: Math.min(contentHeight, 200)
                                    model: folderListModel; spacing: 4; clip: true

                                    delegate: Rectangle {
                                            width: folderListView.width; height: 32
                                            color: "white"; radius: 4
                                            border { color: "#CED4DA"; width: 1 }

                                            Row {
                                                anchors.left: parent.left
                                                anchors.leftMargin: 10
                                                anchors.verticalCenter: parent.verticalCenter
                                                spacing: 8
                                                width: parent.width - 50

                                                Text {
                                                    text: "📁"; font.pixelSize: 14
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }

                                                Text {
                                                    text: model.path; font.pixelSize: 12; color: "#495057"
                                                    anchors.verticalCenter: parent.verticalCenter
                                                    elide: Text.ElideMiddle; width: parent.width - 30
                                                }
                                            }

                                            Text {
                                                anchors { right: parent.right; rightMargin: 10; verticalCenter: parent.verticalCenter }
                                                text: "✕"; color: "#DC3545"; font { pixelSize: 14; bold: true }

                                                MouseArea {
                                                    anchors.fill: parent
                                                    onClicked: {
                                                        var folderPath = folderListModel.get(index).path
                                                        folderListModel.remove(index)
                                                        // 通知后端移除监听文件夹
                                                        if (typeof networkBackend !== 'undefined') {
                                                            networkBackend.removeWatchFolder(folderPath)
                                                        }
                                                    }
                                                }
                                            }
                                    }
                                }
                            }
                        }
                    }
                }
            }

                // 连接设置组件
                Component {
                    id: connectionSettingsComponent

                    Column {
                        spacing: 20
                        width: parent.width

                        // 连接服务器地址设置卡片
                        Rectangle {
                            width: parent.width
                            height: connectionColumn.implicitHeight + 40
                            color: "#F8F9FA"
                            radius: 8
                            border.color: "#E9ECEF"
                            border.width: 1

                            Column {
                                id: connectionColumn
                                anchors.left: parent.left
                                anchors.leftMargin: 20
                                anchors.top: parent.top
                                anchors.topMargin: 20
                                anchors.right: parent.right
                                anchors.rightMargin: 20
                                spacing: 15

                                Row {
                                    width: parent.width
                                    spacing: 10

                                    Text {
                                        text: "连接服务器地址"; font { pixelSize: 16; bold: true }
                                        color: "#495057"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }

                                    Rectangle {
                                        width: 60; height: 24; color: "#28A745"; radius: 4
                                        anchors.verticalCenter: parent.verticalCenter

                                        Text {
                                            anchors.centerIn: parent
                                            text: "应用"; color: "white"
                                            font { pixelSize: 11; bold: true }
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (typeof clientMode !== 'undefined' && ipv4Input.text.trim() !== "") {
                                                    var host = ipv4Input.text.trim()
                                                    var port = parseInt(ipv4PortInput.text) || 3333
                                                    console.log("正在连接到服务器:", host + ":" + port)
                                                    clientMode.connectToServer(host, port)
                                                } else {
                                                    console.log("请输入有效的服务器地址")
                                                }
                                            }
                                        }
                                    }
                                    Rectangle {
                                        width: 60; height: 24; color: "#DC3545"; radius: 4
                                        anchors.verticalCenter: parent.verticalCenter
                                        Text {
                                            anchors.centerIn: parent
                                            text: "停止"; color: "white"
                                            font { pixelSize: 11; bold: true }
                                        }
                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (typeof clientMode !== 'undefined') {
                                                    console.log("断开服务器连接")
                                                    clientMode.disconnectFromServer()
                                                }
                                            }
                                        }
                                    }
                                }

                                // IPv4连接设置
                                Row {
                                    spacing: 15

                                    Text {
                                        text: "IPv4:"; font { pixelSize: 13; bold: true }
                                        color: "#6C757D"; anchors.verticalCenter: parent.verticalCenter
                                    }

                                    Rectangle {
                                        width: 200; height: 30; color: "white"; radius: 6
                                        border { color: "#CED4DA"; width: 1 }
                                        TextInput {
                                            id: ipv4Input
                                            anchors.fill: parent; anchors.margins: 8
                                            font { pixelSize: 13; family: "Consolas, Monaco, monospace" }
                                            color: "#212529"; selectByMouse: true
                                            horizontalAlignment: Text.AlignHCenter; verticalAlignment: Text.AlignVCenter
                                        }
                                        Text {
                                            text: "请输入IPv4地址"; color: "#6C757D"
                                            font { pixelSize: 13; family: "Consolas, Monaco, monospace" }
                                            anchors.centerIn: parent
                                            visible: ipv4Input.text === ""
                                        }
                                    }

                                    Text {
                                        text: "端口:"
                                        font.pixelSize: 13
                                        color: "#6C757D"
                                        font.bold: true
                                        anchors.verticalCenter: parent.verticalCenter
                                    }

                                    Rectangle {
                                        width: 80; height: 30; color: "white"; radius: 6
                                        border { color: "#CED4DA"; width: 1 }
                                        TextInput {
                                            id: ipv4PortInput
                                            anchors.fill: parent; anchors.margins: 8
                                            text: "3333"; font { pixelSize: 13; family: "Consolas, Monaco, monospace" }
                                            color: "#212529"; selectByMouse: true
                                            validator: IntValidator { bottom: 1; top: 65535 }
                                            horizontalAlignment: Text.AlignHCenter; verticalAlignment: Text.AlignVCenter
                                        }
                                    }
                                }

                                // IPv6连接设置
                                Row {
                                    spacing: 15

                                    Text {
                                        text: "IPv6:"; font { pixelSize: 13; bold: true }
                                        color: "#6C757D"; anchors.verticalCenter: parent.verticalCenter
                                    }

                                    Rectangle {
                                        width: 280; height: 30; color: "white"; radius: 6
                                        border { color: "#CED4DA"; width: 1 }
                                        TextInput {
                                            id: ipv6Input
                                            anchors.fill: parent; anchors.margins: 8
                                            font { pixelSize: 13; family: "Consolas, Monaco, monospace" }
                                            color: "#212529"; selectByMouse: true
                                            horizontalAlignment: Text.AlignHCenter; verticalAlignment: Text.AlignVCenter
                                        }
                                        Text {
                                            text: "请输入IPv6地址"; color: "#6C757D"
                                            font { pixelSize: 13; family: "Consolas, Monaco, monospace" }
                                            anchors.centerIn: parent
                                            visible: ipv6Input.text === ""
                                        }
                                    }

                                    Text {
                                        text: "端口:"
                                        font.pixelSize: 13
                                        color: "#6C757D"
                                        font.bold: true
                                        anchors.verticalCenter: parent.verticalCenter
                                    }

                                    Rectangle {
                                        width: 80; height: 30; color: "white"; radius: 6
                                        border { color: "#CED4DA"; width: 1 }
                                        TextInput {
                                            id: ipv6PortInput
                                            anchors.fill: parent; anchors.margins: 8
                                            text: "3333"; font { pixelSize: 13; family: "Consolas, Monaco, monospace" }
                                            color: "#212529"; selectByMouse: true
                                            validator: IntValidator { bottom: 1; top: 65535 }
                                            horizontalAlignment: Text.AlignHCenter; verticalAlignment: Text.AlignVCenter
                                        }
                                    }
                                }
                                }
                        }
                    }
                }
        }
    }
    // 文件夹列表数据模型
    ListModel {id: folderListModel}
    // 菜单数据模型
    ListModel {
        id: menuModel
        ListElement { title: "服务器"; icon: "🖥️"; component: "ServerSettings"; selected: true }
        ListElement { title: "连接"; icon: "🔗"; component: "ConnectionSettings"; selected: false }
    }

    // 文件夹选择对话框
    FolderDialog {
        id: folderDialog
        title: "选择监听文件夹"
        onAccepted: {
            var folderPath = selectedFolder.toString()
            // 移除 file:// 前缀
            if (folderPath.startsWith("file:///")) {
                folderPath = folderPath.substring(8)
            } else if (folderPath.startsWith("file://")) {
                folderPath = folderPath.substring(7)
            }

            // 检查是否已存在
            var exists = false
            for (var i = 0; i < folderListModel.count; i++) {
                if (folderListModel.get(i).path === folderPath) {
                    exists = true
                    break
                }
            }

            if (!exists) {
                folderListModel.append({"path": folderPath})
                // 通知后端添加监听文件夹
                if (typeof networkBackend !== 'undefined') networkBackend.addWatchFolder(folderPath)
            }
        }
    }
}
