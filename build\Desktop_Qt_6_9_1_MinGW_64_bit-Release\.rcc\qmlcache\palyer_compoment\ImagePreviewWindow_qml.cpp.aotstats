[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 8, "durationMicroseconds": 464, "errorMessage": "", "functionName": "x", "line": 15}, {"codegenSuccessful": true, "column": 8, "durationMicroseconds": 189, "errorMessage": "", "functionName": "y", "line": 16}, {"codegenSuccessful": true, "column": 12, "durationMicroseconds": 137, "errorMessage": "", "functionName": "flags", "line": 17}, {"codegenSuccessful": true, "column": 14, "durationMicroseconds": 105, "errorMessage": "", "functionName": "opacity", "line": 19}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 100, "errorMessage": "", "functionName": "closeWindow", "line": 40}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 175, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "loadPreviousImage", "line": 59}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 248, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "loadNextImage", "line": 60}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 248, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "toggleFullScreen", "line": 61}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "loadImageByIndex", "line": 67}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "updateImageSize", "line": 81}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 1038, "errorMessage": "", "functionName": "centerImage", "line": 135}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 73, "errorMessage": "", "functionName": "type", "line": 22}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 191, "errorMessage": "", "functionName": "onTriggered", "line": 36}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 173, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "onTriggered", "line": 49}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 124, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "onCompleted", "line": 145}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 40, "errorMessage": "", "functionName": "fill", "line": 149}, {"codegenSuccessful": false, "column": 29, "durationMicroseconds": 201, "errorMessage": "method loadPreviousImage cannot be resolved.", "functionName": "onLeftPressed", "line": 155}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 127, "errorMessage": "method loadNextImage cannot be resolved.", "functionName": "onRightPressed", "line": 156}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 126, "errorMessage": "method closeWindow cannot be resolved.", "functionName": "onEscapePressed", "line": 157}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 138, "errorMessage": "method updateImageSize cannot be resolved.", "functionName": "onSpacePressed", "line": 158}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 79, "errorMessage": "", "functionName": "width", "line": 163}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 84, "errorMessage": "", "functionName": "onPressed", "line": 168}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 34, "errorMessage": "", "functionName": "fill", "line": 167}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 68, "errorMessage": "", "functionName": "top", "line": 174}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 55, "errorMessage": "", "functionName": "left", "line": 174}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 367, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "model", "line": 178}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 154, "errorMessage": "", "functionName": "radius", "line": 185}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 319, "errorMessage": "Cannot access value for name modelData", "functionName": "color", "line": 186}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 204, "errorMessage": "", "functionName": "opacity", "line": 187}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 156, "errorMessage": "", "functionName": "scale", "line": 188}, {"codegenSuccessful": true, "column": 91, "durationMicroseconds": 100, "errorMessage": "", "functionName": "type", "line": 190}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 82, "errorMessage": "", "functionName": "type", "line": 191}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 353, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 194}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 180, "errorMessage": "", "functionName": "visible", "line": 201}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 67, "errorMessage": "", "functionName": "centerIn", "line": 196}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 177, "errorMessage": "Cannot access value for name modelData", "functionName": "x", "line": 198}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 142, "errorMessage": "Cannot access value for name modelData", "functionName": "y", "line": 199}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 296, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 209}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 81, "errorMessage": "", "functionName": "fill", "line": 208}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 396, "errorMessage": "", "functionName": "width", "line": 219}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 101, "errorMessage": "", "functionName": "height", "line": 219}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 72, "errorMessage": "", "functionName": "centerIn", "line": 218}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 564, "errorMessage": "Cannot access value for name imageIndex", "functionName": "text", "line": 224}, {"codegenSuccessful": true, "column": 42, "durationMicroseconds": 99, "errorMessage": "", "functionName": "horizontalAlignment", "line": 225}, {"codegenSuccessful": true, "column": 80, "durationMicroseconds": 80, "errorMessage": "", "functionName": "verticalAlignment", "line": 225}, {"codegenSuccessful": true, "column": 106, "durationMicroseconds": 72, "errorMessage": "", "functionName": "elide", "line": 225}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 181, "errorMessage": "", "functionName": "opacity", "line": 226}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 79, "errorMessage": "", "functionName": "centerIn", "line": 223}, {"codegenSuccessful": true, "column": 68, "durationMicroseconds": 131, "errorMessage": "", "functionName": "visible", "line": 231}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 199, "errorMessage": "", "functionName": "opacity", "line": 232}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 135, "errorMessage": "", "functionName": "fill", "line": 231}, {"codegenSuccessful": true, "column": 58, "durationMicroseconds": 38, "errorMessage": "", "functionName": "fill", "line": 238}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 173, "errorMessage": "", "functionName": "width", "line": 248}, {"codegenSuccessful": false, "column": 23, "durationMicroseconds": 340, "errorMessage": "Cannot access value for name imageIndex", "functionName": "text", "line": 251}, {"codegenSuccessful": true, "column": 19, "durationMicroseconds": 161, "errorMessage": "", "functionName": "y", "line": 252}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 156, "errorMessage": "", "functionName": "right", "line": 245}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 56, "errorMessage": "", "functionName": "width", "line": 260}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 194, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 261}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 80, "errorMessage": "", "functionName": "top", "line": 259}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 146, "errorMessage": "", "functionName": "width", "line": 272}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 48, "errorMessage": "", "functionName": "contentWidth", "line": 273}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 42, "errorMessage": "", "functionName": "contentHeight", "line": 273}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 142, "errorMessage": "", "functionName": "top", "line": 268}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 58, "errorMessage": "", "functionName": "bottom", "line": 269}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 57, "errorMessage": "", "functionName": "horizontalCenter", "line": 270}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 75, "errorMessage": "", "functionName": "policy", "line": 282}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 133, "errorMessage": "", "functionName": "visible", "line": 283}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 162, "errorMessage": "", "functionName": "opacity", "line": 285}, {"codegenSuccessful": true, "column": 85, "durationMicroseconds": 73, "errorMessage": "", "functionName": "type", "line": 287}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 77, "errorMessage": "", "functionName": "radius", "line": 290}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 112, "errorMessage": "Cannot load property pressed from (component in C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml)::parent with type QQuickItem.", "functionName": "color", "line": 291}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 158, "errorMessage": "", "functionName": "acceptedButtons", "line": 299}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 53, "errorMessage": "", "functionName": "cursor<PERSON><PERSON>pe", "line": 299}, {"codegenSuccessful": true, "column": 17, "durationMicroseconds": 114, "errorMessage": "", "functionName": "showCursor", "line": 303}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 2928, "errorMessage": "", "functionName": "onPositionChanged", "line": 309}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 72, "errorMessage": "", "functionName": "onPositionChanged", "line": 309}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 67, "errorMessage": "", "functionName": "onEntered", "line": 324}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 214, "errorMessage": "", "functionName": "onExited", "line": 325}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 410, "errorMessage": "", "functionName": "onPressed", "line": 326}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 62, "errorMessage": "", "functionName": "onPressed", "line": 326}, {"codegenSuccessful": true, "column": 29, "durationMicroseconds": 135, "errorMessage": "", "functionName": "onReleased", "line": 327}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 7594, "errorMessage": "", "functionName": "onWheel", "line": 330}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 68, "errorMessage": "", "functionName": "onWheel", "line": 330}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 329, "errorMessage": "", "functionName": "fill", "line": 298}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 319, "errorMessage": "", "functionName": "fillMode", "line": 368}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 257, "errorMessage": "", "functionName": "opacity", "line": 371}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 289, "errorMessage": "Cannot generate efficient code for incomparable types QString and (component in C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml)::source with type QUrl", "functionName": "onSourceChanged", "line": 375}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 542, "errorMessage": "method updateImageSize cannot be resolved.", "functionName": "onStatusChanged", "line": 376}, {"codegenSuccessful": true, "column": 85, "durationMicroseconds": 385, "errorMessage": "", "functionName": "type", "line": 373}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 234, "errorMessage": "", "functionName": "running", "line": 389}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 73, "errorMessage": "", "functionName": "visible", "line": 390}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 68, "errorMessage": "", "functionName": "centerIn", "line": 388}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 116, "errorMessage": "Cannot load property _showErrorOverlay from (component in C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml)::parent with type QQuickItem.", "functionName": "visible", "line": 395}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 173, "errorMessage": "", "functionName": "fill", "line": 396}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 86, "errorMessage": "", "functionName": "centerIn", "line": 400}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 124, "errorMessage": "", "functionName": "horizontalCenter", "line": 404}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 554, "errorMessage": "Cannot access value for name imageIndex", "functionName": "text", "line": 413}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 125, "errorMessage": "", "functionName": "horizontalCenter", "line": 412}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 120, "errorMessage": "", "functionName": "horizontalCenter", "line": 419}], "filePath": "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml"}], "moduleId": "palyer(palyer)"}]