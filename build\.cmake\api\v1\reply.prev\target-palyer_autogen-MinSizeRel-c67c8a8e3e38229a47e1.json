{"backtrace": 0, "backtraceGraph": {"commands": ["add_dependencies", "_qt_internal_qml_copy_files_to_build_dir", "qt6_target_qml_sources", "qt6_add_qml_module", "qt_add_qml_module"], "files": ["CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake"], "nodes": [{"file": 0}, {"command": 4, "file": 0, "line": 27, "parent": 0}, {"command": 3, "file": 1, "line": 1252, "parent": 1}, {"command": 2, "file": 1, "line": 916, "parent": 2}, {"command": 1, "file": 1, "line": 3497, "parent": 3}, {"command": 0, "file": 1, "line": 2860, "parent": 4}, {"command": 1, "file": 1, "line": 3503, "parent": 3}, {"command": 0, "file": 1, "line": 2860, "parent": 6}]}, "dependencies": [{"backtrace": 0, "id": "palyer_qmlimportscan::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "palyer_copy_qml::@6890427a1f51a3e7e1df"}, {"backtrace": 7, "id": "palyer_copy_res::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "backend::@e17dcb4e28158375c849"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "palyer_autogen::@6890427a1f51a3e7e1df", "isGeneratorProvided": true, "name": "palyer_autogen", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/palyer_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/60efe0b784417ad25bcf545a28e72a55/palyer_autogen.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}