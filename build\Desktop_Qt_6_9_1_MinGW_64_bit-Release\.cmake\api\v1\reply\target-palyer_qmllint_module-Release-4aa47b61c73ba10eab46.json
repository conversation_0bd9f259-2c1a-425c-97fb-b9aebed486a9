{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_target_enable_qmllint", "qt6_target_qml_sources", "qt6_add_qml_module", "qt_add_qml_module"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 4, "file": 1, "line": 27, "parent": 0}, {"command": 3, "file": 0, "line": 1252, "parent": 1}, {"command": 2, "file": 0, "line": 916, "parent": 2}, {"command": 1, "file": 0, "line": 3228, "parent": 3}, {"command": 0, "file": 0, "line": 1561, "parent": 4}]}, "dependencies": [{"id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df"}], "folder": {"name": "/QmlLinter"}, "id": "palyer_qmllint_module::@6890427a1f51a3e7e1df", "name": "palyer_qmllint_module", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/palyer_qmllint_module", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/palyer_qmllint_module.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}