import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickmultieffect_p.h"
        name: "QQuickMultiEffect"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Effects/MultiEffect 6.5",
            "QtQuick.Effects/MultiEffect 6.7"
        ]
        exportMetaObjectRevisions: [1541, 1543]
        Property {
            name: "source"
            type: "QQuickItem"
            isPointer: true
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "autoPaddingEnabled"
            type: "bool"
            read: "autoPaddingEnabled"
            write: "setAutoPaddingEnabled"
            notify: "autoPaddingEnabledChanged"
            index: 1
        }
        Property {
            name: "paddingRect"
            type: "QRectF"
            read: "paddingRect"
            write: "setPaddingRect"
            notify: "paddingRectChanged"
            index: 2
        }
        Property {
            name: "brightness"
            type: "double"
            read: "brightness"
            write: "setBrightness"
            notify: "brightnessChanged"
            index: 3
        }
        Property {
            name: "contrast"
            type: "double"
            read: "contrast"
            write: "setContrast"
            notify: "contrastChanged"
            index: 4
        }
        Property {
            name: "saturation"
            type: "double"
            read: "saturation"
            write: "setSaturation"
            notify: "saturationChanged"
            index: 5
        }
        Property {
            name: "colorization"
            type: "double"
            read: "colorization"
            write: "setColorization"
            notify: "colorizationChanged"
            index: 6
        }
        Property {
            name: "colorizationColor"
            type: "QColor"
            read: "colorizationColor"
            write: "setColorizationColor"
            notify: "colorizationColorChanged"
            index: 7
        }
        Property {
            name: "blurEnabled"
            type: "bool"
            read: "blurEnabled"
            write: "setBlurEnabled"
            notify: "blurEnabledChanged"
            index: 8
        }
        Property {
            name: "blur"
            type: "double"
            read: "blur"
            write: "setBlur"
            notify: "blurChanged"
            index: 9
        }
        Property {
            name: "blurMax"
            type: "int"
            read: "blurMax"
            write: "setBlurMax"
            notify: "blurMaxChanged"
            index: 10
        }
        Property {
            name: "blurMultiplier"
            type: "double"
            read: "blurMultiplier"
            write: "setBlurMultiplier"
            notify: "blurMultiplierChanged"
            index: 11
        }
        Property {
            name: "shadowEnabled"
            type: "bool"
            read: "shadowEnabled"
            write: "setShadowEnabled"
            notify: "shadowEnabledChanged"
            index: 12
        }
        Property {
            name: "shadowOpacity"
            type: "double"
            read: "shadowOpacity"
            write: "setShadowOpacity"
            notify: "shadowOpacityChanged"
            index: 13
        }
        Property {
            name: "shadowBlur"
            type: "double"
            read: "shadowBlur"
            write: "setShadowBlur"
            notify: "shadowBlurChanged"
            index: 14
        }
        Property {
            name: "shadowHorizontalOffset"
            type: "double"
            read: "shadowHorizontalOffset"
            write: "setShadowHorizontalOffset"
            notify: "shadowHorizontalOffsetChanged"
            index: 15
        }
        Property {
            name: "shadowVerticalOffset"
            type: "double"
            read: "shadowVerticalOffset"
            write: "setShadowVerticalOffset"
            notify: "shadowVerticalOffsetChanged"
            index: 16
        }
        Property {
            name: "shadowColor"
            type: "QColor"
            read: "shadowColor"
            write: "setShadowColor"
            notify: "shadowColorChanged"
            index: 17
        }
        Property {
            name: "shadowScale"
            type: "double"
            read: "shadowScale"
            write: "setShadowScale"
            notify: "shadowScaleChanged"
            index: 18
        }
        Property {
            name: "maskEnabled"
            type: "bool"
            read: "maskEnabled"
            write: "setMaskEnabled"
            notify: "maskEnabledChanged"
            index: 19
        }
        Property {
            name: "maskSource"
            type: "QQuickItem"
            isPointer: true
            read: "maskSource"
            write: "setMaskSource"
            notify: "maskSourceChanged"
            index: 20
        }
        Property {
            name: "maskThresholdMin"
            type: "double"
            read: "maskThresholdMin"
            write: "setMaskThresholdMin"
            notify: "maskThresholdMinChanged"
            index: 21
        }
        Property {
            name: "maskSpreadAtMin"
            type: "double"
            read: "maskSpreadAtMin"
            write: "setMaskSpreadAtMin"
            notify: "maskSpreadAtMinChanged"
            index: 22
        }
        Property {
            name: "maskThresholdMax"
            type: "double"
            read: "maskThresholdMax"
            write: "setMaskThresholdMax"
            notify: "maskThresholdMaxChanged"
            index: 23
        }
        Property {
            name: "maskSpreadAtMax"
            type: "double"
            read: "maskSpreadAtMax"
            write: "setMaskSpreadAtMax"
            notify: "maskSpreadAtMaxChanged"
            index: 24
        }
        Property {
            name: "maskInverted"
            type: "bool"
            read: "maskInverted"
            write: "setMaskInverted"
            notify: "maskInvertedChanged"
            index: 25
        }
        Property {
            name: "itemRect"
            type: "QRectF"
            read: "itemRect"
            notify: "itemRectChanged"
            index: 26
            isReadonly: true
        }
        Property {
            name: "fragmentShader"
            type: "QString"
            read: "fragmentShader"
            notify: "fragmentShaderChanged"
            index: 27
            isReadonly: true
        }
        Property {
            name: "vertexShader"
            type: "QString"
            read: "vertexShader"
            notify: "vertexShaderChanged"
            index: 28
            isReadonly: true
        }
        Property {
            name: "hasProxySource"
            type: "bool"
            read: "hasProxySource"
            notify: "hasProxySourceChanged"
            index: 29
            isReadonly: true
        }
        Signal { name: "shaderChanged" }
        Signal { name: "itemSizeChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "autoPaddingEnabledChanged" }
        Signal { name: "paddingRectChanged" }
        Signal { name: "brightnessChanged" }
        Signal { name: "contrastChanged" }
        Signal { name: "saturationChanged" }
        Signal { name: "colorizationChanged" }
        Signal { name: "colorizationColorChanged" }
        Signal { name: "blurEnabledChanged" }
        Signal { name: "blurChanged" }
        Signal { name: "blurMaxChanged" }
        Signal { name: "blurMultiplierChanged" }
        Signal { name: "shadowEnabledChanged" }
        Signal { name: "shadowOpacityChanged" }
        Signal { name: "shadowBlurChanged" }
        Signal { name: "shadowHorizontalOffsetChanged" }
        Signal { name: "shadowVerticalOffsetChanged" }
        Signal { name: "shadowColorChanged" }
        Signal { name: "shadowScaleChanged" }
        Signal { name: "maskEnabledChanged" }
        Signal { name: "maskSourceChanged" }
        Signal { name: "maskThresholdMinChanged" }
        Signal { name: "maskSpreadAtMinChanged" }
        Signal { name: "maskThresholdMaxChanged" }
        Signal { name: "maskSpreadAtMaxChanged" }
        Signal { name: "maskInvertedChanged" }
        Signal { name: "itemRectChanged" }
        Signal { name: "fragmentShaderChanged" }
        Signal { name: "vertexShaderChanged" }
        Signal { name: "hasProxySourceChanged" }
    }
    Component {
        file: "private/qquickrectangularshadow_p.h"
        name: "QQuickRectangularShadow"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: ["QtQuick.Effects/RectangularShadow 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "offset"
            type: "QVector2D"
            read: "offset"
            write: "setOffset"
            notify: "offsetChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "blur"
            type: "double"
            read: "blur"
            write: "setBlur"
            notify: "blurChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "radius"
            type: "double"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "spread"
            type: "double"
            read: "spread"
            write: "setSpread"
            notify: "spreadChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "cached"
            type: "bool"
            read: "isCached"
            write: "setCached"
            notify: "cachedChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "material"
            type: "QQuickItem"
            isPointer: true
            read: "material"
            write: "setMaterial"
            notify: "materialChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "offsetChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "blurChanged" }
        Signal { name: "radiusChanged" }
        Signal { name: "spreadChanged" }
        Signal { name: "cachedChanged" }
        Signal { name: "materialChanged" }
    }
}
