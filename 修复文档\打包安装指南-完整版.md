# Palyer 打包安装完整指南

## 📋 **Release文件夹结构分析**

基于检测到的Release文件夹，包含以下关键组件：

### **🔧 核心程序文件**
- `palyer.exe` - 主程序
- `libbackend.dll` - 后端库

### **📚 Qt运行时库 (约40个DLL)**
- **核心库**: Qt6Core.dll, Qt6Gui.dll, Qt6Quick.dll, Qt6Qml.dll
- **网络库**: Qt6Network.dll
- **多媒体**: Qt6Multimedia.dll, Qt6MultimediaQuick.dll
- **控件库**: Qt6QuickControls2*.dll (多个样式)
- **对话框**: Qt6QuickDialogs2*.dll
- **效果库**: Qt6QuickEffects.dll

### **🎬 多媒体支持**
- **FFmpeg库**: avcodec-61.dll, avformat-61.dll, avutil-59.dll, swresample-5.dll, swscale-8.dll
- **工具**: ffmpeg.exe, avifdec.exe

### **🖼️ 图像处理**
- **WebP工具**: cwebp.exe, dwebp.exe
- **图像格式插件**: imageformats/ (支持JPEG, PNG, WebP, SVG, TIFF等)

### **🔌 Qt插件目录**
- `platforms/` - 平台插件 (qwindows.dll)
- `qml/` - QML模块 (QtQuick, QtMultimedia等)
- `multimedia/` - 多媒体插件
- `networkinformation/` - 网络信息插件
- `tls/` - TLS安全插件
- `styles/` - 样式插件
- `iconengines/` - 图标引擎

### **🌐 MinGW运行时**
- libgcc_s_seh-1.dll
- libstdc++-6.dll  
- libwinpthread-1.dll

## 🎁 **NSIS安装脚本特性**

### **📦 安装组件**
1. **核心程序** (必需) - 主程序和基础运行时
2. **可选样式** - Material、Fusion、Imagine等UI主题
3. **虚拟键盘支持** - 触屏设备支持
4. **调试工具** - QML开发调试工具

### **🔧 安装功能**
- ✅ 自动检测已安装版本并提示卸载
- ✅ 创建桌面和开始菜单快捷方式
- ✅ 注册到Windows程序列表
- ✅ 支持完整卸载
- ✅ 中文界面支持

### **📊 预估大小**
- **源文件**: ~150-200MB
- **安装包**: ~80-120MB (压缩后)
- **安装后**: ~200-250MB

## 🚀 **打包步骤**

### **1. 准备工作**
```bash
# 确保Release版本已编译
# 检查路径: build\Desktop_Qt_6_9_1_MinGW_64_bit-Release\palyer.exe
```

### **2. 安装NSIS**
- 下载地址: https://nsis.sourceforge.io/Download
- 安装到默认路径: `C:\Program Files (x86)\NSIS\`

### **3. 运行打包脚本**
```bash
# 方法1: 使用简化脚本
build-installer-simple.bat

# 方法2: 手动编译NSIS脚本
"C:\Program Files (x86)\NSIS\makensis.exe" palyer-installer.nsi
```

### **4. 输出结果**
- 生成文件: `Palyer-1.0.0-Setup.exe`
- 位置: 项目根目录

## 📋 **文件清单**

### **必需文件**
```
palyer-installer.nsi          # NSIS安装脚本
build-installer-simple.bat    # 打包脚本
LICENSE.txt                   # 许可证文件
build\Desktop_Qt_6_9_1_MinGW_64_bit-Release\  # Release文件夹
```

### **生成文件**
```
Palyer-1.0.0-Setup.exe       # 最终安装包
```

## 🧪 **测试清单**

### **安装测试**
- [ ] 在干净的Windows 10/11系统上安装
- [ ] 验证所有文件正确复制
- [ ] 检查快捷方式创建
- [ ] 确认程序能正常启动

### **功能测试**
- [ ] 图片浏览功能正常
- [ ] 支持的图片格式 (JPEG, PNG, WebP, AVIF等)
- [ ] 视频播放功能
- [ ] 网络共享功能
- [ ] 图片转换功能
- [ ] 缩略图生成

### **卸载测试**
- [ ] 完整卸载不留残留文件
- [ ] 注册表项正确清理
- [ ] 快捷方式正确删除

## ⚠️ **注意事项**

### **路径要求**
- 源码路径不能包含中文字符
- NSIS安装路径使用默认位置
- Release文件夹必须完整

### **依赖检查**
- 确保所有Qt DLL都已部署
- FFmpeg库完整
- 图像格式插件齐全

### **兼容性**
- 目标系统: Windows 10/11 64位
- 需要Visual C++ 2019运行时 (通常系统已有)
- 不支持Windows 7/8

## 🔧 **故障排除**

### **编译失败**
```bash
# 检查NSIS路径
"C:\Program Files (x86)\NSIS\makensis.exe" --version

# 检查源文件
dir "build\Desktop_Qt_6_9_1_MinGW_64_bit-Release\palyer.exe"

# 语法检查
"C:\Program Files (x86)\NSIS\makensis.exe" /V4 palyer-installer.nsi
```

### **安装失败**
- 以管理员身份运行安装包
- 检查目标系统是否为64位
- 确认有足够磁盘空间 (至少500MB)

### **运行时错误**
- 检查Qt DLL是否完整
- 验证FFmpeg库版本匹配
- 确认图像格式插件正确部署

## 📈 **优化建议**

### **减小体积**
- 移除不需要的翻译文件
- 排除调试工具组件
- 使用UPX压缩可执行文件

### **提升体验**
- 添加安装进度动画
- 自定义安装图标
- 添加安装完成后的启动选项

这个打包方案确保了Palyer的所有功能都能在目标系统上正常工作，包括图片浏览、网络共享、多媒体播放等核心功能。
