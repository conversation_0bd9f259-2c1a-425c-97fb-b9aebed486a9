import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qabstractitemmodel.h"
        name: "QAbstractTableModel"
        accessSemantics: "reference"
        prototype: "QAbstractItemModel"
    }
    Component {
        file: "private/qqmldelegatechooser_p.h"
        name: "QQmlDelegateChooser"
        accessSemantics: "reference"
        defaultProperty: "choices"
        prototype: "QQmlAbstractDelegateComponent"
        exports: [
            "Qt.labs.qmlmodels/DelegateChooser 1.0",
            "Qt.labs.qmlmodels/DelegateChooser 2.0",
            "Qt.labs.qmlmodels/DelegateChooser 6.0"
        ]
        exportMetaObjectRevisions: [256, 512, 1536]
        Property {
            name: "role"
            type: "QString"
            read: "role"
            write: "setRole"
            notify: "roleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "choices"
            type: "QQmlDelegateChoice"
            isList: true
            read: "choices"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal { name: "roleChanged" }
    }
    Component {
        file: "private/qqmltablemodel_p.h"
        name: "QQmlTableModel"
        accessSemantics: "reference"
        defaultProperty: "columns"
        prototype: "QAbstractTableModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "Qt.labs.qmlmodels/TableModel 1.0",
            "Qt.labs.qmlmodels/TableModel 6.0",
            "Qt.labs.qmlmodels/TableModel 6.4"
        ]
        exportMetaObjectRevisions: [256, 1536, 1540]
        Property {
            name: "columnCount"
            type: "int"
            read: "columnCount"
            notify: "columnCountChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rowCount"
            type: "int"
            read: "rowCount"
            notify: "rowCountChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rows"
            type: "QVariant"
            read: "rows"
            write: "setRows"
            notify: "rowsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "columns"
            type: "QQmlTableModelColumn"
            isList: true
            read: "columns"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal { name: "columnCountChanged" }
        Signal { name: "rowCountChanged" }
        Signal { name: "rowsChanged" }
        Method {
            name: "appendRow"
            Parameter { name: "row"; type: "QVariant" }
        }
        Method { name: "clear" }
        Method {
            name: "getRow"
            type: "QVariant"
            Parameter { name: "rowIndex"; type: "int" }
        }
        Method {
            name: "insertRow"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "row"; type: "QVariant" }
        }
        Method {
            name: "moveRow"
            Parameter { name: "fromRowIndex"; type: "int" }
            Parameter { name: "toRowIndex"; type: "int" }
            Parameter { name: "rows"; type: "int" }
        }
        Method {
            name: "moveRow"
            isCloned: true
            Parameter { name: "fromRowIndex"; type: "int" }
            Parameter { name: "toRowIndex"; type: "int" }
        }
        Method {
            name: "removeRow"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "rows"; type: "int" }
        }
        Method {
            name: "removeRow"
            isCloned: true
            Parameter { name: "rowIndex"; type: "int" }
        }
        Method {
            name: "setRow"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "row"; type: "QVariant" }
        }
        Method {
            name: "data"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "QString" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qqmltablemodelcolumn_p.h"
        name: "QQmlTableModelColumn"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt.labs.qmlmodels/TableModelColumn 1.0",
            "Qt.labs.qmlmodels/TableModelColumn 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "display"
            type: "QJSValue"
            read: "display"
            write: "setDisplay"
            notify: "displayChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "setDisplay"
            type: "QJSValue"
            read: "getSetDisplay"
            write: "setSetDisplay"
            notify: "setDisplayChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "decoration"
            type: "QJSValue"
            read: "decoration"
            write: "setDecoration"
            notify: "decorationChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "setDecoration"
            type: "QJSValue"
            read: "getSetDecoration"
            write: "setSetDecoration"
            notify: "setDecorationChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "edit"
            type: "QJSValue"
            read: "edit"
            write: "setEdit"
            notify: "editChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "setEdit"
            type: "QJSValue"
            read: "getSetEdit"
            write: "setSetEdit"
            notify: "setEditChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "toolTip"
            type: "QJSValue"
            read: "toolTip"
            write: "setToolTip"
            notify: "toolTipChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "setToolTip"
            type: "QJSValue"
            read: "getSetToolTip"
            write: "setSetToolTip"
            notify: "setToolTipChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "statusTip"
            type: "QJSValue"
            read: "statusTip"
            write: "setStatusTip"
            notify: "statusTipChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "setStatusTip"
            type: "QJSValue"
            read: "getSetStatusTip"
            write: "setSetStatusTip"
            notify: "setStatusTipChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "whatsThis"
            type: "QJSValue"
            read: "whatsThis"
            write: "setWhatsThis"
            notify: "whatsThisChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "setWhatsThis"
            type: "QJSValue"
            read: "getSetWhatsThis"
            write: "setSetWhatsThis"
            notify: "setWhatsThisChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "font"
            type: "QJSValue"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "setFont"
            type: "QJSValue"
            read: "getSetFont"
            write: "setSetFont"
            notify: "setFontChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "textAlignment"
            type: "QJSValue"
            read: "textAlignment"
            write: "setTextAlignment"
            notify: "textAlignmentChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "setTextAlignment"
            type: "QJSValue"
            read: "getSetTextAlignment"
            write: "setSetTextAlignment"
            notify: "setTextAlignmentChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "background"
            type: "QJSValue"
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "setBackground"
            type: "QJSValue"
            read: "getSetBackground"
            write: "setSetBackground"
            notify: "setBackgroundChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "foreground"
            type: "QJSValue"
            read: "foreground"
            write: "setForeground"
            notify: "foregroundChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "setForeground"
            type: "QJSValue"
            read: "getSetForeground"
            write: "setSetForeground"
            notify: "setForegroundChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "checkState"
            type: "QJSValue"
            read: "checkState"
            write: "setCheckState"
            notify: "checkStateChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "setCheckState"
            type: "QJSValue"
            read: "getSetCheckState"
            write: "setSetCheckState"
            notify: "setCheckStateChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "accessibleText"
            type: "QJSValue"
            read: "accessibleText"
            write: "setAccessibleText"
            notify: "accessibleTextChanged"
            index: 22
            isFinal: true
        }
        Property {
            name: "setAccessibleText"
            type: "QJSValue"
            read: "getSetAccessibleText"
            write: "setSetAccessibleText"
            notify: "setAccessibleTextChanged"
            index: 23
            isFinal: true
        }
        Property {
            name: "accessibleDescription"
            type: "QJSValue"
            read: "accessibleDescription"
            write: "setAccessibleDescription"
            notify: "accessibleDescriptionChanged"
            index: 24
            isFinal: true
        }
        Property {
            name: "setAccessibleDescription"
            type: "QJSValue"
            read: "getSetAccessibleDescription"
            write: "setSetAccessibleDescription"
            notify: "setAccessibleDescriptionChanged"
            index: 25
            isFinal: true
        }
        Property {
            name: "sizeHint"
            type: "QJSValue"
            read: "sizeHint"
            write: "setSizeHint"
            notify: "sizeHintChanged"
            index: 26
            isFinal: true
        }
        Property {
            name: "setSizeHint"
            type: "QJSValue"
            read: "getSetSizeHint"
            write: "setSetSizeHint"
            notify: "setSizeHintChanged"
            index: 27
            isFinal: true
        }
        Signal { name: "indexChanged" }
        Signal { name: "displayChanged" }
        Signal { name: "setDisplayChanged" }
        Signal { name: "decorationChanged" }
        Signal { name: "setDecorationChanged" }
        Signal { name: "editChanged" }
        Signal { name: "setEditChanged" }
        Signal { name: "toolTipChanged" }
        Signal { name: "setToolTipChanged" }
        Signal { name: "statusTipChanged" }
        Signal { name: "setStatusTipChanged" }
        Signal { name: "whatsThisChanged" }
        Signal { name: "setWhatsThisChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "setFontChanged" }
        Signal { name: "textAlignmentChanged" }
        Signal { name: "setTextAlignmentChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "setBackgroundChanged" }
        Signal { name: "foregroundChanged" }
        Signal { name: "setForegroundChanged" }
        Signal { name: "checkStateChanged" }
        Signal { name: "setCheckStateChanged" }
        Signal { name: "accessibleTextChanged" }
        Signal { name: "setAccessibleTextChanged" }
        Signal { name: "accessibleDescriptionChanged" }
        Signal { name: "setAccessibleDescriptionChanged" }
        Signal { name: "sizeHintChanged" }
        Signal { name: "setSizeHintChanged" }
    }
}
