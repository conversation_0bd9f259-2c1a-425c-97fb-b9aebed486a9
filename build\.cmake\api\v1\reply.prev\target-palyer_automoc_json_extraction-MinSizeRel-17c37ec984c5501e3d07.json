{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "qt6_extract_metatypes", "_qt_internal_qml_type_registration", "qt6_add_qml_module", "qt_add_qml_module", "add_dependencies"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 4, "file": 2, "line": 27, "parent": 0}, {"command": 3, "file": 1, "line": 1252, "parent": 1}, {"command": 2, "file": 1, "line": 796, "parent": 2}, {"command": 1, "file": 1, "line": 3704, "parent": 3}, {"command": 0, "file": 0, "line": 1355, "parent": 4}, {"command": 5, "file": 0, "line": 1370, "parent": 4}]}, "dependencies": [{"backtrace": 6, "id": "palyer_autogen::@6890427a1f51a3e7e1df"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "palyer_automoc_json_extraction::@6890427a1f51a3e7e1df", "name": "palyer_automoc_json_extraction", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "build/CMakeFiles/palyer_automoc_json_extraction", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/60efe0b784417ad25bcf545a28e72a55/palyer_automoc_json_extraction.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}