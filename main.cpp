#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QImageReader>
#include <QThreadPool>
#include <QLoggingCategory>
#include <QSurfaceFormat>
#include <QTimer>
#include "ImageProcessor.h"
#include "ImageConversionManager.h"
#include "NetworkBackend.h"
#include "server/ClientMode.h"

int main(int argc, char *argv[])
{
    // 设置高刷新率和禁用垂直同步
    QSurfaceFormat format = QSurfaceFormat::defaultFormat();
    format.setSwapInterval(0); // 禁用垂直同步
    format.setSwapBehavior(QSurfaceFormat::TripleBuffer); // 使用三重缓冲
    QSurfaceFormat::setDefaultFormat(format);
    
    // 图像处理相关优化 - 增加内存限制以支持大图片
    qputenv("QT_IMAGEIO_MAXALLOC", "4096"); // 增加到4GB
    
    QImageReader::setAllocationLimit(4096); // 增加到4GB
    
    // 启用SIMD加速
    #if defined(Q_OS_WIN) || defined(Q_OS_LINUX)
        qputenv("QT_ENABLE_AVX2", "1");
    #endif
    
    QGuiApplication app(argc, argv);
    app.setOrganizationName("Demo");
    app.setOrganizationDomain("Demo.com");
    app.setApplicationName("APP");

    // Register our custom type for cross-thread signals/slots
    qRegisterMetaType<ConversionTask>("ConversionTask");
    qRegisterMetaType<QList<ConversionTask>>("QList<ConversionTask>");

    // 创建独立的图像管理器
    ImageProcessor* imageProcessor = new ImageProcessor(&app);
    ImageConversionManager* imageConversionManager = new ImageConversionManager(&app);
    NetworkBackend* networkBackend = new NetworkBackend(&app);
    ClientMode* clientMode = new ClientMode(&app);

    // 关键：设置ImageProcessor引用到NetworkBackend
    networkBackend->setImageProcessor(imageProcessor);

    QQmlApplicationEngine engine;

    engine.rootContext()->setContextProperty("imageProcessor", imageProcessor);
    engine.rootContext()->setContextProperty("imageConversionManager", imageConversionManager);
    engine.rootContext()->setContextProperty("networkBackend", networkBackend);
    engine.rootContext()->setContextProperty("clientMode", clientMode);
    
    // 注册类型
    qmlRegisterType<ImageProcessor>("backend", 1, 0, "ImageProcessor");
    qmlRegisterType<ImageConversionManager>("backend", 1, 0, "ImageConversionManager");
    
    // 连接信号并设置
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreationFailed,
                    &app, []() { QCoreApplication::exit(-1); }, Qt::QueuedConnection);

    qmlRegisterSingletonType(QUrl("qrc:/compoment/AppState.qml"), "palyer.Singleton", 1, 0, "AppState");
    QLoggingCategory::setFilterRules("qt.multimedia.*=false");

    // 添加事件循环监视器，确保UI不会被长时间阻塞
    QTimer* watchdogTimer = new QTimer(&app);
    watchdogTimer->setInterval(500); // 500ms检查一次
    // 这个空槽函数确保事件循环继续运行
    // 避免事件循环被长时间阻塞
    QObject::connect(watchdogTimer, &QTimer::timeout, &app, []() {});
    watchdogTimer->start();

    engine.loadFromModule("palyer", "Main");

    // 程序退出时清理网络服务
    QObject::connect(&app, &QGuiApplication::aboutToQuit, [networkBackend]() {
        networkBackend->stopImageServer();
    });

    return app.exec();
}
