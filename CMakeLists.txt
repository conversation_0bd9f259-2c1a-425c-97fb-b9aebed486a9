cmake_minimum_required(VERSION 3.16)

project(palyer VERSION 0.1 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

find_package(Qt6 REQUIRED COMPONENTS Quick Core Gui Qml quickeffects Multimedia)

# 添加backend文件夹
add_subdirectory(backend)

qt_add_executable(palyer
    main.cpp
    res.qrc
)

# 优化编译选项
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    target_compile_options(palyer PRIVATE -O2)
elseif(MSVC)
    target_compile_options(palyer PRIVATE /O2 /Ot /MP)
endif()

file(GLOB_RECURSE QML_FILES RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} "compoment/*.qml")
qt_add_qml_module(palyer
    URI palyer
    VERSION 1.0
    QML_FILES Main.qml ${QML_FILES}
)

set_target_properties(palyer PROPERTIES
    MACOSX_BUNDLE_GUI_IDENTIFIER my.example.com
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

target_link_libraries(palyer
    PRIVATE Qt6::Quick Qt6::Core Qt6::Gui Qt6::Qml Qt6::Multimedia
)

# 链接backend库
target_link_libraries(palyer
    PRIVATE backend
)

# Qt部署配置 - 自动复制Qt运行时依赖
if(QT_KNOWN_POLICY_QTP0001)
    qt_policy(SET QTP0001 NEW)
endif()

# Windows平台使用windeployqt部署Qt依赖
if(WIN32)
    # 获取windeployqt路径
    get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
    get_filename_component(QT_WINDEPLOYQT_EXECUTABLE ${QT_QMAKE_EXECUTABLE} PATH)
    set(QT_WINDEPLOYQT_EXECUTABLE "${QT_WINDEPLOYQT_EXECUTABLE}/windeployqt.exe")

    # 在构建后自动部署Qt依赖（仅复制必需的库）
    add_custom_command(TARGET palyer POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E echo "Deploying Qt runtime dependencies..."
        COMMAND "${QT_WINDEPLOYQT_EXECUTABLE}" --qmldir "${CMAKE_SOURCE_DIR}" --no-translations --no-system-d3d-compiler --no-opengl-sw --no-compiler-runtime "$<TARGET_FILE:palyer>"
        COMMENT "Deploying minimal Qt runtime dependencies with windeployqt"
    )
endif()

include(GNUInstallDirs)
install(TARGETS palyer
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

# 确保 backend 库也被安装到正确位置
install(TARGETS backend
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

install(FILES
    ${CMAKE_BINARY_DIR}/backend.dll
    DESTINATION ${CMAKE_INSTALL_BINDIR}
)
