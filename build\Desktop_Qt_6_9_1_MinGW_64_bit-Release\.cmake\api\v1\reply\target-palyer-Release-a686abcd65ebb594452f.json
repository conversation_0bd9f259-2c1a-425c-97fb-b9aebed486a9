{"artifacts": [{"path": "palyer.exe"}, {"path": "palyer.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "qt6_add_qml_module", "qt_add_qml_module", "add_dependencies", "_qt_internal_qml_copy_files_to_build_dir", "qt6_target_qml_sources", "_qt_internal_scan_qml_imports", "_qt_internal_generate_deploy_qml_imports_script", "cmake_language", "_qt_internal_finalize_executable", "qt6_finalize_target", "target_compile_options", "target_include_directories", "_qt_internal_qml_type_registration", "target_sources", "qt6_extract_metatypes", "set_source_files_properties", "__qt_propagate_generated_resource", "_qt_internal_process_resource", "qt6_add_resources", "_qt_internal_target_enable_qmlcachegen", "_qt_internal_expose_deferred_files_to_ide"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake:787:EVAL"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 14, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 71, "parent": 0}, {"command": 5, "file": 1, "line": 41, "parent": 0}, {"command": 5, "file": 1, "line": 46, "parent": 0}, {"command": 8, "file": 1, "line": 9, "parent": 0}, {"file": 4, "parent": 8}, {"command": 8, "file": 4, "line": 218, "parent": 9}, {"file": 3, "parent": 10}, {"command": 7, "file": 3, "line": 55, "parent": 11}, {"file": 2, "parent": 12}, {"command": 6, "file": 2, "line": 61, "parent": 13}, {"command": 7, "file": 3, "line": 43, "parent": 11}, {"file": 9, "parent": 15}, {"command": 10, "file": 9, "line": 45, "parent": 16}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 18}, {"file": 6, "parent": 19}, {"command": 7, "file": 6, "line": 55, "parent": 20}, {"file": 5, "parent": 21}, {"command": 6, "file": 5, "line": 61, "parent": 22}, {"command": 12, "file": 1, "line": 27, "parent": 0}, {"command": 11, "file": 10, "line": 1252, "parent": 24}, {"command": 5, "file": 10, "line": 596, "parent": 25}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 27}, {"file": 12, "parent": 28}, {"command": 7, "file": 12, "line": 55, "parent": 29}, {"file": 11, "parent": 30}, {"command": 6, "file": 11, "line": 61, "parent": 31}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 33}, {"file": 14, "parent": 34}, {"command": 7, "file": 14, "line": 58, "parent": 35}, {"file": 13, "parent": 36}, {"command": 6, "file": 13, "line": 61, "parent": 37}, {"command": 7, "file": 14, "line": 46, "parent": 35}, {"file": 17, "parent": 39}, {"command": 10, "file": 17, "line": 45, "parent": 40}, {"command": 9, "file": 8, "line": 137, "parent": 41}, {"command": 8, "file": 7, "line": 76, "parent": 42}, {"file": 16, "parent": 43}, {"command": 7, "file": 16, "line": 55, "parent": 44}, {"file": 15, "parent": 45}, {"command": 6, "file": 15, "line": 61, "parent": 46}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 9, "file": 8, "line": 137, "parent": 17}, {"command": 8, "file": 7, "line": 76, "parent": 49}, {"file": 19, "parent": 50}, {"command": 7, "file": 19, "line": 57, "parent": 51}, {"file": 18, "parent": 52}, {"command": 6, "file": 18, "line": 61, "parent": 53}, {"command": 7, "file": 19, "line": 45, "parent": 51}, {"file": 22, "parent": 55}, {"command": 10, "file": 22, "line": 46, "parent": 56}, {"command": 9, "file": 8, "line": 137, "parent": 57}, {"command": 8, "file": 7, "line": 76, "parent": 58}, {"file": 21, "parent": 59}, {"command": 7, "file": 21, "line": 55, "parent": 60}, {"file": 20, "parent": 61}, {"command": 6, "file": 20, "line": 61, "parent": 62}, {"command": 15, "file": 10, "line": 916, "parent": 25}, {"command": 14, "file": 10, "line": 3503, "parent": 64}, {"command": 13, "file": 10, "line": 2860, "parent": 65}, {"command": 14, "file": 10, "line": 3497, "parent": 64}, {"command": 13, "file": 10, "line": 2860, "parent": 67}, {"file": 1, "line": -1, "parent": 0}, {"command": 20, "file": 23, "line": 1, "parent": 69}, {"command": 19, "file": 0, "line": 818, "parent": 70}, {"command": 18, "file": 0, "line": 740, "parent": 71}, {"command": 17, "file": 0, "line": 740, "parent": 72}, {"command": 16, "file": 10, "line": 4401, "parent": 73}, {"command": 13, "file": 10, "line": 4176, "parent": 74}, {"command": 21, "file": 1, "line": 21, "parent": 0}, {"command": 23, "file": 10, "line": 796, "parent": 25}, {"command": 22, "file": 10, "line": 3879, "parent": 77}, {"command": 22, "file": 10, "line": 3904, "parent": 77}, {"command": 25, "file": 10, "line": 3704, "parent": 77}, {"command": 24, "file": 0, "line": 1486, "parent": 80}, {"command": 24, "file": 10, "line": 3870, "parent": 77}, {"command": 26, "file": 10, "line": 3894, "parent": 77}, {"command": 29, "file": 10, "line": 850, "parent": 25}, {"command": 28, "file": 0, "line": 401, "parent": 84}, {"command": 27, "file": 0, "line": 2528, "parent": 85}, {"command": 24, "file": 0, "line": 2072, "parent": 86}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 30, "file": 10, "line": 3342, "parent": 64}, {"command": 24, "file": 10, "line": 1696, "parent": 89}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 24, "file": 10, "line": 3205, "parent": 64}, {"command": 24, "file": 10, "line": 3398, "parent": 64}, {"command": 29, "file": 10, "line": 3519, "parent": 64}, {"command": 28, "file": 0, "line": 401, "parent": 106}, {"command": 27, "file": 0, "line": 2528, "parent": 107}, {"command": 24, "file": 0, "line": 2072, "parent": 108}, {"command": 31, "file": 0, "line": 812, "parent": 70}, {"command": 24, "file": 0, "line": 2197, "parent": 110}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -fdiagnostics-color=always"}, {"backtrace": 76, "fragment": "-O2"}], "defines": [{"backtrace": 48, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 48, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 48, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 26, "define": "QT_NETWORK_LIB"}, {"backtrace": 48, "define": "QT_NO_DEBUG"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 26, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 6, "define": "QT_QMLMETA_LIB"}, {"backtrace": 6, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 6, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 26, "define": "QT_QML_LIB"}, {"backtrace": 6, "define": "QT_QUICK_LIB"}, {"backtrace": 48, "define": "UNICODE"}, {"backtrace": 48, "define": "WIN32"}, {"backtrace": 48, "define": "WIN64"}, {"backtrace": 48, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 48, "define": "_UNICODE"}, {"backtrace": 48, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/include"}, {"backtrace": 78, "path": "C:/Qt/file/palyer"}, {"backtrace": 7, "path": "C:/Qt/file/palyer/backend"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQuick"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlModels"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtMultimedia"}], "language": "CXX", "sourceIndexes": [0, 1, 5, 7, 8, 10, 12, 14, 16, 18, 20, 22, 23, 28]}, {"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -fdiagnostics-color=always"}, {"backtrace": 76, "fragment": "-O2"}, {"backtrace": 83, "fragment": "-Wa,-mbig-obj"}], "defines": [{"backtrace": 48, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 48, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 48, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 26, "define": "QT_NETWORK_LIB"}, {"backtrace": 48, "define": "QT_NO_DEBUG"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 26, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 6, "define": "QT_QMLMETA_LIB"}, {"backtrace": 6, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 6, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 26, "define": "QT_QML_LIB"}, {"backtrace": 6, "define": "QT_QUICK_LIB"}, {"backtrace": 48, "define": "UNICODE"}, {"backtrace": 48, "define": "WIN32"}, {"backtrace": 48, "define": "WIN64"}, {"backtrace": 48, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 48, "define": "_UNICODE"}, {"backtrace": 48, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/include"}, {"backtrace": 78, "path": "C:/Qt/file/palyer"}, {"backtrace": 7, "path": "C:/Qt/file/palyer/backend"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQml"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration"}, {"backtrace": 79, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQuick"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlModels"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtMultimedia"}], "language": "CXX", "sourceIndexes": [4]}], "dependencies": [{"backtrace": 66, "id": "palyer_copy_res::@6890427a1f51a3e7e1df"}, {"backtrace": 68, "id": "palyer_copy_qml::@6890427a1f51a3e7e1df"}, {"backtrace": 75, "id": "palyer_qmlimportscan::@6890427a1f51a3e7e1df"}, {"backtrace": 7, "id": "backend::@e17dcb4e28158375c849"}, {"backtrace": 0, "id": "palyer_autogen::@6890427a1f51a3e7e1df"}, {"id": "palyer_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}], "id": "palyer::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/palyer"}}, "link": {"commandFragments": [{"fragment": "-O3 -DNDEBUG", "role": "flags"}, {"fragment": "-mwindows", "role": "flags"}, {"backtrace": 6, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Multimedia.a", "role": "libraries"}, {"backtrace": 7, "fragment": "backend\\libbackend.dll.a", "role": "libraries"}, {"backtrace": 6, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Quick.a", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlMeta.a", "role": "libraries"}, {"backtrace": 23, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlWorkerScript.a", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6QmlModels.a", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Qml.a", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6OpenGL.a", "role": "libraries"}, {"backtrace": 14, "fragment": "-luser32", "role": "libraries"}, {"backtrace": 6, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 32, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 32, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 32, "fragment": "-ldxguid", "role": "libraries"}, {"backtrace": 32, "fragment": "-ld3d12", "role": "libraries"}, {"backtrace": 38, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 47, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 54, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 54, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"backtrace": 63, "fragment": "-lmingw32", "role": "libraries"}, {"backtrace": 63, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 38, "fragment": "-lshell32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "palyer", "nameOnDisk": "palyer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 6, 9, 11, 13, 15, 17, 19, 21, 28]}, {"name": "Resources", "sourceIndexes": [2]}, {"name": "Source Files\\Generated", "sourceIndexes": [3, 4, 5, 7, 8, 10, 12, 14, 16, 18, 20, 22, 23, 24, 25, 26]}, {"name": "", "sourceIndexes": [27, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]}, {"name": "CMake Rules", "sourceIndexes": [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "res.qrc", "sourceGroupIndex": 1}, {"backtrace": 81, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/meta_types/qt6palyer_release_metatypes.json.gen", "sourceGroupIndex": 2}, {"backtrace": 82, "compileGroupIndex": 1, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_qmltyperegistrations.cpp", "sourceGroupIndex": 2}, {"backtrace": 87, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qt/rcc/qrc_qmake_palyer.cpp", "sourceGroupIndex": 2}, {"backtrace": 88, "path": "Main.qml", "sourceGroupIndex": 0}, {"backtrace": 90, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_qmlcache_loader.cpp", "sourceGroupIndex": 2}, {"backtrace": 91, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_Main_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 92, "path": "compoment/AppState.qml", "sourceGroupIndex": 0}, {"backtrace": 93, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/AppState_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 94, "path": "compoment/ImagePreviewWindow.qml", "sourceGroupIndex": 0}, {"backtrace": 95, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/ImagePreviewWindow_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 96, "path": "compoment/NetworkSettingsDialog.qml", "sourceGroupIndex": 0}, {"backtrace": 97, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/NetworkSettingsDialog_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 98, "path": "compoment/SettingsPage.qml", "sourceGroupIndex": 0}, {"backtrace": 99, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/SettingsPage_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 100, "path": "compoment/imagePage.qml", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/imagePage_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 102, "path": "compoment/template/BaseWindowTemplate.qml", "sourceGroupIndex": 0}, {"backtrace": 103, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/template/BaseWindowTemplate_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 104, "path": "compoment/toolbar/ConversionWindow.qml", "sourceGroupIndex": 0}, {"backtrace": 105, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/toolbar/ConversionWindow_qml.cpp", "sourceGroupIndex": 2}, {"backtrace": 109, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qt/rcc/qrc_palyer_raw_qml_0.cpp", "sourceGroupIndex": 2}, {"backtrace": 111, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qt/rcc/qmake_palyer.qrc", "sourceGroupIndex": 2}, {"backtrace": 111, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/qmldir", "sourceGroupIndex": 2}, {"backtrace": 111, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qt/rcc/palyer_raw_qml_0.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/EWIEGA46WW/qrc_res.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/meta_types/qt6palyer_release_metatypes.json.gen.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qt/rcc/qrc_qmake_palyer.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_qmlcache_loader.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_Main_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/AppState_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/ImagePreviewWindow_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/NetworkSettingsDialog_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/SettingsPage_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/imagePage_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/template/BaseWindowTemplate_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.rcc/qmlcache/palyer_compoment/toolbar/ConversionWindow_qml.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qt/rcc/qrc_palyer_raw_qml_0.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/timestamp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/EWIEGA46WW/qrc_res.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/meta_types/palyer_json_file_list.txt.rule", "sourceGroupIndex": 4}, {"backtrace": 48, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6core_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 26, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qml_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 26, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6network_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 6, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6quick_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 6, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6gui_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 6, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlmeta_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 6, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlmodels_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 6, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6qmlworkerscript_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 6, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6opengl_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 6, "path": "C:/Qt/6.9.1/mingw_64/metatypes/qt6multimedia_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}